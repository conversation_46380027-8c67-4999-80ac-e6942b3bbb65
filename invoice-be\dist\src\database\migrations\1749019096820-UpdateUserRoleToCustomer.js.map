{"version": 3, "file": "1749019096820-UpdateUserRoleToCustomer.js", "sourceRoot": "", "sources": ["../../../../src/database/migrations/1749019096820-UpdateUserRoleToCustomer.ts"], "names": [], "mappings": ";;;AAEA,MAAa,qCAAqC;IAGzC,KAAK,CAAC,EAAE,CAAC,WAAwB;QAEtC,MAAM,WAAW,CAAC,KAAK,CAAC;;;SAGnB,CAAC,CAAC;QAGP,MAAM,WAAW,CAAC,KAAK,CAAC;;SAEnB,CAAC,CAAC;QAEP,MAAM,WAAW,CAAC,KAAK,CAAC;;SAEnB,CAAC,CAAC;QAEP,MAAM,WAAW,CAAC,KAAK,CAAC;;;;SAInB,CAAC,CAAC;QAEP,MAAM,WAAW,CAAC,KAAK,CAAC;;SAEnB,CAAC,CAAC;QAGP,MAAM,WAAW,CAAC,KAAK,CAAC;;;;SAInB,CAAC,CAAC;QAGP,MAAM,WAAW,CAAC,KAAK,CAAC;;SAEnB,CAAC,CAAC;QAEP,MAAM,WAAW,CAAC,KAAK,CAAC;;SAEnB,CAAC,CAAC;QAEP,MAAM,WAAW,CAAC,KAAK,CAAC;;;;SAInB,CAAC,CAAC;QAEP,MAAM,WAAW,CAAC,KAAK,CAAC;;SAEnB,CAAC,CAAC;QAGP,MAAM,WAAW,CAAC,KAAK,CAAC;;;SAGnB,CAAC,CAAC;IACT,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QAExC,MAAM,WAAW,CAAC,KAAK,CAAC;;;;SAInB,CAAC,CAAC;QAGP,MAAM,WAAW,CAAC,KAAK,CAAC;;SAEnB,CAAC,CAAC;QAEP,MAAM,WAAW,CAAC,KAAK,CAAC;;SAEnB,CAAC,CAAC;QAEP,MAAM,WAAW,CAAC,KAAK,CAAC;;;;SAInB,CAAC,CAAC;QAEP,MAAM,WAAW,CAAC,KAAK,CAAC;;SAEnB,CAAC,CAAC;IACT,CAAC;CACF;AAzFD,sFAyFC"}