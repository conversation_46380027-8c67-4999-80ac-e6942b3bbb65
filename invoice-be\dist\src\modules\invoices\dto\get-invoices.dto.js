"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GetInvoicesDto = void 0;
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
class GetInvoicesDto {
    apiToken;
    invoiceNo;
    startDate;
    endDate;
    invoiceType;
    rowPerPage;
    pageNum;
    buyerTaxCode;
    buyerIdNo;
    templateCode;
    invoiceSeri;
    getAll;
    issueStartDate;
    issueEndDate;
    adjustmentType;
}
exports.GetInvoicesDto = GetInvoicesDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Customer API token for authentication',
        required: true,
    }),
    (0, class_validator_1.IsNotEmpty)({ message: 'API token is required' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], GetInvoicesDto.prototype, "apiToken", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Invoice number',
        required: false,
        minLength: 7,
        maxLength: 35,
        pattern: '^[a-zA-Z0-9]*$',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MinLength)(7, { message: 'Invoice number must be at least 7 characters' }),
    (0, class_validator_1.MaxLength)(35, { message: 'Invoice number must not exceed 35 characters' }),
    (0, class_validator_1.Matches)(/^[a-zA-Z0-9]*$/, {
        message: 'Invoice number must contain only alphanumeric characters',
    }),
    __metadata("design:type", String)
], GetInvoicesDto.prototype, "invoiceNo", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Start date for invoice search',
        required: true,
        maxLength: 50,
        example: '2024-01-01',
    }),
    (0, class_validator_1.IsNotEmpty)({ message: 'Start date is required' }),
    (0, class_validator_1.IsDateString)({}, { message: 'Start date must be a valid date string' }),
    (0, class_validator_1.MaxLength)(50, { message: 'Start date must not exceed 50 characters' }),
    __metadata("design:type", String)
], GetInvoicesDto.prototype, "startDate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'End date for invoice search',
        required: true,
        maxLength: 50,
        example: '2024-03-31',
    }),
    (0, class_validator_1.IsNotEmpty)({ message: 'End date is required' }),
    (0, class_validator_1.IsDateString)({}, { message: 'End date must be a valid date string' }),
    (0, class_validator_1.MaxLength)(50, { message: 'End date must not exceed 50 characters' }),
    __metadata("design:type", String)
], GetInvoicesDto.prototype, "endDate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Invoice type',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], GetInvoicesDto.prototype, "invoiceType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Number of rows per page',
        required: true,
        minimum: 1,
        example: 10,
    }),
    (0, class_validator_1.IsNotEmpty)({ message: 'Rows per page is required' }),
    (0, class_validator_1.IsNumber)({}, { message: 'Rows per page must be a number' }),
    (0, class_validator_1.Min)(1, { message: 'Rows per page must be at least 1' }),
    __metadata("design:type", Number)
], GetInvoicesDto.prototype, "rowPerPage", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Page number (1-based)',
        required: true,
        minimum: 1,
        example: 1,
    }),
    (0, class_validator_1.IsNotEmpty)({ message: 'Page number is required' }),
    (0, class_validator_1.IsNumber)({}, { message: 'Page number must be a number' }),
    (0, class_validator_1.Min)(0, { message: 'Page number must be at least 0' }),
    __metadata("design:type", Number)
], GetInvoicesDto.prototype, "pageNum", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Buyer tax code',
        required: false,
        maxLength: 20,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(20, { message: 'Buyer tax code must not exceed 20 characters' }),
    __metadata("design:type", String)
], GetInvoicesDto.prototype, "buyerTaxCode", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Buyer ID number',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], GetInvoicesDto.prototype, "buyerIdNo", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Template code',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], GetInvoicesDto.prototype, "templateCode", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Invoice series',
        required: false,
        maxLength: 25,
        pattern: '^[a-zA-Z0-9]*$',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(25, { message: 'Invoice series must not exceed 25 characters' }),
    (0, class_validator_1.Matches)(/^[a-zA-Z0-9]*$/, {
        message: 'Invoice series must contain only alphanumeric characters',
    }),
    __metadata("design:type", String)
], GetInvoicesDto.prototype, "invoiceSeri", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Get all invoices',
        required: false,
        example: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)({ message: 'Get all must be a boolean value' }),
    __metadata("design:type", Boolean)
], GetInvoicesDto.prototype, "getAll", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Issue start date',
        required: false,
        maxLength: 50,
        example: '2024-01-01',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)({}, { message: 'Issue start date must be a valid date string' }),
    (0, class_validator_1.MaxLength)(50, { message: 'Issue start date must not exceed 50 characters' }),
    __metadata("design:type", String)
], GetInvoicesDto.prototype, "issueStartDate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Issue end date',
        required: false,
        maxLength: 50,
        example: '2024-03-31',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)({}, { message: 'Issue end date must be a valid date string' }),
    (0, class_validator_1.MaxLength)(50, { message: 'Issue end date must not exceed 50 characters' }),
    __metadata("design:type", String)
], GetInvoicesDto.prototype, "issueEndDate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Adjustment type',
        required: false,
        maxLength: 1,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(1, { message: 'Adjustment type must not exceed 1 character' }),
    __metadata("design:type", String)
], GetInvoicesDto.prototype, "adjustmentType", void 0);
//# sourceMappingURL=get-invoices.dto.js.map