import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { SinvoiceApiLoginResponse } from '../dto/sinvoice-login.dto';
export declare class SinvoiceService {
    private readonly httpService;
    private readonly configService;
    private readonly logger;
    private readonly sinvoiceApiUrl;
    constructor(httpService: HttpService, configService: ConfigService);
    login(username: string, password: string): Promise<SinvoiceApiLoginResponse>;
    refreshToken(refreshToken: string): Promise<SinvoiceApiLoginResponse>;
    validateAccessToken(accessToken: string): Promise<boolean>;
}
