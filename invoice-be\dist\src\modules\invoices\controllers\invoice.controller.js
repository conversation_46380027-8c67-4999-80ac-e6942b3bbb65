"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.InvoiceController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const throttler_1 = require("@nestjs/throttler");
const invoice_service_1 = require("../services/invoice.service");
const create_invoice_dto_1 = require("../dto/create-invoice.dto");
const public_decorator_1 = require("../../../common/decorators/public.decorator");
const get_invoices_dto_1 = require("../dto/get-invoices.dto");
let InvoiceController = class InvoiceController {
    invoiceService;
    constructor(invoiceService) {
        this.invoiceService = invoiceService;
    }
    async createInvoice(apiToken, createInvoiceDto) {
        if (!apiToken) {
            throw new common_1.UnauthorizedException('API token is required');
        }
        return this.invoiceService.createInvoice(apiToken, createInvoiceDto);
    }
    async getInvoices(getInvoicesDto) {
        const { apiToken, ...searchParams } = getInvoicesDto;
        if (!apiToken) {
            throw new common_1.UnauthorizedException('API token is required');
        }
        return this.invoiceService.getInvoices(apiToken, searchParams);
    }
};
exports.InvoiceController = InvoiceController;
__decorate([
    (0, common_1.Post)('create'),
    (0, public_decorator_1.Public)(),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, throttler_1.Throttle)({ default: { limit: 5, ttl: 60000 } }),
    (0, swagger_1.ApiOperation)({
        summary: 'Create a new invoice using Sinvoice API',
        description: `Creates a new invoice using customer credentials and Sinvoice API.
    
This endpoint requires customer authentication using an API token. The token should be included in the X-API-Token header.
The API token can be obtained from the customer's profile in the system.

The endpoint will:
1. Validate the API token
2. Use the customer's stored Sinvoice credentials for authentication
3. Create the invoice through Sinvoice API
4. Return the Sinvoice API response

Rate limit: 5 requests per minute per customer.`,
    }),
    (0, swagger_1.ApiSecurity)('X-API-Token'),
    (0, swagger_1.ApiBody)({ type: create_invoice_dto_1.CreateInvoiceDto }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Invoice created successfully',
        schema: {
            example: {
                status: 'success',
                message: 'Invoice created successfully',
                data: {
                    invoiceId: '*********',
                    invoiceNumber: 'AA/20E-0001',
                    createdAt: '2024-03-20T10:00:00.000Z',
                },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: 'Bad request - invalid request format',
        schema: {
            example: {
                statusCode: 400,
                message: 'Invalid request format',
                error: 'Bad Request',
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 401,
        description: 'Unauthorized - invalid API token or Sinvoice credentials',
        schema: {
            example: {
                statusCode: 401,
                message: 'Invalid API token',
                error: 'Unauthorized',
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 500,
        description: 'Internal server error - Sinvoice API connection failed',
        schema: {
            example: {
                statusCode: 500,
                message: 'Sinvoice API connection failed',
                error: 'Internal Server Error',
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 429,
        description: 'Too many requests - rate limit exceeded',
        schema: {
            example: {
                statusCode: 429,
                message: 'Too Many Requests',
                error: 'Rate limit exceeded. Try again in 60 seconds.',
            },
        },
    }),
    __param(0, (0, common_1.Headers)('x-api-token')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, create_invoice_dto_1.CreateInvoiceDto]),
    __metadata("design:returntype", Promise)
], InvoiceController.prototype, "createInvoice", null);
__decorate([
    (0, common_1.Post)('list'),
    (0, public_decorator_1.Public)(),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, throttler_1.Throttle)({ default: { limit: 10, ttl: 60000 } }),
    (0, swagger_1.ApiOperation)({
        summary: 'Get invoices using Sinvoice API',
        description: `Retrieves invoices using customer credentials and Sinvoice API.
    
This endpoint requires customer authentication using an API token provided in the request body.
The API token can be obtained from the customer's profile in the system.

The endpoint will:
1. Validate the API token
2. Use the customer's stored Sinvoice credentials for authentication
3. Retrieve invoices through Sinvoice API
4. Return the Sinvoice API response

Rate limit: 10 requests per minute per customer.`,
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Invoices retrieved successfully',
        schema: {
            example: {
                status: 'success',
                message: 'Invoices retrieved successfully',
                data: {
                    totalRecords: 100,
                    pageNum: 0,
                    rowPerPage: 10,
                    invoices: [
                        {
                            invoiceId: '*********',
                            invoiceNo: 'AA/20E-0001',
                            issueDate: '2024-03-20T10:00:00.000Z',
                            buyerTaxCode: '0*********',
                            total: 1000000,
                            status: 'SIGNED',
                        },
                    ],
                },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: 'Bad request - invalid request format',
        schema: {
            example: {
                statusCode: 400,
                message: 'Invalid request format',
                error: 'Bad Request',
                details: [
                    'API token is required',
                    'Start date is required',
                    'End date is required',
                    'Rows per page must be at least 1',
                ],
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 401,
        description: 'Unauthorized - invalid API token',
        schema: {
            example: {
                statusCode: 401,
                message: 'Invalid API token',
                error: 'Unauthorized',
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 500,
        description: 'Internal server error - Sinvoice API connection failed',
        schema: {
            example: {
                statusCode: 500,
                message: 'Sinvoice API connection failed',
                error: 'Internal Server Error',
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 429,
        description: 'Too many requests - rate limit exceeded',
        schema: {
            example: {
                statusCode: 429,
                message: 'Too Many Requests',
                error: 'Rate limit exceeded. Try again in 60 seconds.',
            },
        },
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [get_invoices_dto_1.GetInvoicesDto]),
    __metadata("design:returntype", Promise)
], InvoiceController.prototype, "getInvoices", null);
exports.InvoiceController = InvoiceController = __decorate([
    (0, swagger_1.ApiTags)('Invoices'),
    (0, common_1.Controller)('invoices'),
    __metadata("design:paramtypes", [invoice_service_1.InvoiceService])
], InvoiceController);
//# sourceMappingURL=invoice.controller.js.map