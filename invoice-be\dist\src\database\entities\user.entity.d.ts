export declare enum UserRole {
    ADMIN = "admin",
    CUSTOMER = "customer"
}
export declare class User {
    id: string;
    username: string;
    email: string;
    password: string;
    role: UserRole;
    isActive: boolean;
    lastLoginAt: Date;
    createdAt: Date;
    updatedAt: Date;
    hashPassword(): Promise<void>;
    validatePassword(password: string): Promise<boolean>;
    toJSON(): Omit<this, "password" | "hashPassword" | "validatePassword" | "toJSON">;
}
