"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CustomerModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const axios_1 = require("@nestjs/axios");
const customer_entity_1 = require("../../database/entities/customer.entity");
const user_entity_1 = require("../../database/entities/user.entity");
const customer_service_1 = require("./services/customer.service");
const sinvoice_service_1 = require("./services/sinvoice.service");
const customer_controller_1 = require("./controllers/customer.controller");
let CustomerModule = class CustomerModule {
};
exports.CustomerModule = CustomerModule;
exports.CustomerModule = CustomerModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([customer_entity_1.Customer, user_entity_1.User]),
            axios_1.HttpModule.register({
                timeout: 10000,
                maxRedirects: 3,
            }),
        ],
        controllers: [customer_controller_1.CustomerController],
        providers: [customer_service_1.CustomerService, sinvoice_service_1.SinvoiceService],
        exports: [customer_service_1.CustomerService, sinvoice_service_1.SinvoiceService],
    })
], CustomerModule);
//# sourceMappingURL=customer.module.js.map