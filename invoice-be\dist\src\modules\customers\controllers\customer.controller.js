"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CustomerController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const throttler_1 = require("@nestjs/throttler");
const customer_service_1 = require("../services/customer.service");
const dto_1 = require("../dto");
const roles_decorator_1 = require("../../../common/decorators/roles.decorator");
const current_user_decorator_1 = require("../../../common/decorators/current-user.decorator");
const user_entity_1 = require("../../../database/entities/user.entity");
let CustomerController = class CustomerController {
    customerService;
    constructor(customerService) {
        this.customerService = customerService;
    }
    async createCustomer(createCustomerDto) {
        return this.customerService.createCustomer(createCustomerDto);
    }
    async getAllCustomers() {
        return this.customerService.getAllCustomers();
    }
    async getCurrentCustomer(user) {
        return this.customerService.getCustomerByUserId(user.id);
    }
    async getCustomerById(id) {
        return this.customerService.getCustomerById(id);
    }
    async updateCurrentCustomer(user, updateCustomerDto) {
        const customer = await this.customerService.getCustomerByUserId(user.id);
        return this.customerService.updateCustomer(customer.id, updateCustomerDto);
    }
    async updateCustomer(id, updateCustomerDto) {
        return this.customerService.updateCustomer(id, updateCustomerDto);
    }
    async resetApiToken(id, resetApiTokenDto) {
        if (resetApiTokenDto.confirmation !== 'RESET_TOKEN') {
            throw new common_1.BadRequestException('Invalid confirmation. Please provide "RESET_TOKEN" as confirmation.');
        }
        return this.customerService.resetApiToken(id);
    }
    async sinvoiceLogin(sinvoiceLoginDto) {
        return this.customerService.sinvoiceLogin(sinvoiceLoginDto.apiToken);
    }
    async deleteCustomer(id) {
        await this.customerService.deleteCustomer(id);
    }
};
exports.CustomerController = CustomerController;
__decorate([
    (0, common_1.Post)(),
    (0, common_1.HttpCode)(common_1.HttpStatus.CREATED),
    (0, roles_decorator_1.Roles)(user_entity_1.UserRole.ADMIN),
    (0, throttler_1.Throttle)({ default: { limit: 10, ttl: 60000 } }),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new customer account' }),
    (0, swagger_1.ApiBody)({ type: dto_1.CreateCustomerDto }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Customer created successfully',
        type: dto_1.CustomerResponseDto,
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: 'Bad request - validation failed',
    }),
    (0, swagger_1.ApiResponse)({
        status: 409,
        description: 'Conflict - username or email already exists',
    }),
    (0, swagger_1.ApiResponse)({
        status: 403,
        description: 'Forbidden - admin role required',
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_1.CreateCustomerDto]),
    __metadata("design:returntype", Promise)
], CustomerController.prototype, "createCustomer", null);
__decorate([
    (0, common_1.Get)(),
    (0, roles_decorator_1.Roles)(user_entity_1.UserRole.ADMIN),
    (0, swagger_1.ApiOperation)({ summary: 'Get all customers (admin only)' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Customers retrieved successfully',
        type: [dto_1.CustomerResponseDto],
    }),
    (0, swagger_1.ApiResponse)({
        status: 403,
        description: 'Forbidden - admin role required',
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], CustomerController.prototype, "getAllCustomers", null);
__decorate([
    (0, common_1.Get)('me'),
    (0, roles_decorator_1.Roles)(user_entity_1.UserRole.CUSTOMER),
    (0, swagger_1.ApiOperation)({ summary: 'Get current customer profile' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Customer profile retrieved successfully',
        type: dto_1.CustomerResponseDto,
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Customer not found',
    }),
    (0, swagger_1.ApiResponse)({
        status: 403,
        description: 'Forbidden - customer role required',
    }),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [user_entity_1.User]),
    __metadata("design:returntype", Promise)
], CustomerController.prototype, "getCurrentCustomer", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, roles_decorator_1.Roles)(user_entity_1.UserRole.ADMIN),
    (0, swagger_1.ApiOperation)({ summary: 'Get customer by ID (admin only)' }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'Customer ID',
        example: '123e4567-e89b-12d3-a456-************',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Customer retrieved successfully',
        type: dto_1.CustomerResponseDto,
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Customer not found',
    }),
    (0, swagger_1.ApiResponse)({
        status: 403,
        description: 'Forbidden - admin role required',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CustomerController.prototype, "getCustomerById", null);
__decorate([
    (0, common_1.Put)('me'),
    (0, roles_decorator_1.Roles)(user_entity_1.UserRole.CUSTOMER),
    (0, swagger_1.ApiOperation)({ summary: 'Update current customer profile' }),
    (0, swagger_1.ApiBody)({ type: dto_1.UpdateCustomerDto }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Customer updated successfully',
        type: dto_1.CustomerResponseDto,
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: 'Bad request - validation failed',
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Customer not found',
    }),
    (0, swagger_1.ApiResponse)({
        status: 409,
        description: 'Conflict - email already exists',
    }),
    (0, swagger_1.ApiResponse)({
        status: 403,
        description: 'Forbidden - customer role required',
    }),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [user_entity_1.User,
        dto_1.UpdateCustomerDto]),
    __metadata("design:returntype", Promise)
], CustomerController.prototype, "updateCurrentCustomer", null);
__decorate([
    (0, common_1.Put)(':id'),
    (0, roles_decorator_1.Roles)(user_entity_1.UserRole.ADMIN),
    (0, swagger_1.ApiOperation)({ summary: 'Update customer by ID (admin only)' }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'Customer ID',
        example: '123e4567-e89b-12d3-a456-************',
    }),
    (0, swagger_1.ApiBody)({ type: dto_1.UpdateCustomerDto }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Customer updated successfully',
        type: dto_1.CustomerResponseDto,
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: 'Bad request - validation failed',
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Customer not found',
    }),
    (0, swagger_1.ApiResponse)({
        status: 409,
        description: 'Conflict - email already exists',
    }),
    (0, swagger_1.ApiResponse)({
        status: 403,
        description: 'Forbidden - admin role required',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, dto_1.UpdateCustomerDto]),
    __metadata("design:returntype", Promise)
], CustomerController.prototype, "updateCustomer", null);
__decorate([
    (0, common_1.Post)(':id/reset-token'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, roles_decorator_1.Roles)(user_entity_1.UserRole.ADMIN),
    (0, throttler_1.Throttle)({ default: { limit: 5, ttl: 60000 } }),
    (0, swagger_1.ApiOperation)({ summary: 'Reset customer API token (admin only)' }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'Customer ID',
        example: '123e4567-e89b-12d3-a456-************',
    }),
    (0, swagger_1.ApiBody)({ type: dto_1.ResetApiTokenDto }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'API token reset successfully',
        type: dto_1.ApiTokenResponseDto,
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: 'Bad request - invalid confirmation',
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Customer not found',
    }),
    (0, swagger_1.ApiResponse)({
        status: 403,
        description: 'Forbidden - admin role required',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, dto_1.ResetApiTokenDto]),
    __metadata("design:returntype", Promise)
], CustomerController.prototype, "resetApiToken", null);
__decorate([
    (0, common_1.Post)('sinvoice/login'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, throttler_1.Throttle)({ default: { limit: 5, ttl: 60000 } }),
    (0, swagger_1.ApiOperation)({
        summary: 'Login to Sinvoice using customer API token',
        description: 'Authenticate with Sinvoice API using customer credentials and return access tokens',
    }),
    (0, swagger_1.ApiBody)({ type: dto_1.SinvoiceLoginDto }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Sinvoice login successful',
        type: dto_1.SinvoiceLoginResponseDto,
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: 'Bad request - invalid API token format',
    }),
    (0, swagger_1.ApiResponse)({
        status: 401,
        description: 'Unauthorized - invalid API token or Sinvoice credentials',
    }),
    (0, swagger_1.ApiResponse)({
        status: 500,
        description: 'Internal server error - Sinvoice API connection failed',
    }),
    (0, swagger_1.ApiResponse)({
        status: 429,
        description: 'Too many requests - rate limit exceeded',
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_1.SinvoiceLoginDto]),
    __metadata("design:returntype", Promise)
], CustomerController.prototype, "sinvoiceLogin", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    (0, roles_decorator_1.Roles)(user_entity_1.UserRole.ADMIN),
    (0, swagger_1.ApiOperation)({ summary: 'Delete customer by ID (admin only)' }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'Customer ID',
        example: '123e4567-e89b-12d3-a456-************',
    }),
    (0, swagger_1.ApiResponse)({
        status: 204,
        description: 'Customer deleted successfully',
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Customer not found',
    }),
    (0, swagger_1.ApiResponse)({
        status: 403,
        description: 'Forbidden - admin role required',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CustomerController.prototype, "deleteCustomer", null);
exports.CustomerController = CustomerController = __decorate([
    (0, swagger_1.ApiTags)('Customers'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.Controller)('customers'),
    __metadata("design:paramtypes", [customer_service_1.CustomerService])
], CustomerController);
//# sourceMappingURL=customer.controller.js.map