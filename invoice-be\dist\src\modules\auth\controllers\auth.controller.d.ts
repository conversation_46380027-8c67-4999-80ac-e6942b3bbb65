import { AuthService } from '../services/auth.service';
import { LoginDto, AuthResponseDto, UserInfoDto } from '../dto';
import { User } from '../../../database/entities/user.entity';
export declare class AuthController {
    private readonly authService;
    constructor(authService: AuthService);
    login(loginDto: LoginDto): Promise<AuthResponseDto>;
    getProfile(user: User): Promise<UserInfoDto>;
    logout(): Promise<{
        message: string;
    }>;
}
