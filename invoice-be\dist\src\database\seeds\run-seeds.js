"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.runSeeds = runSeeds;
const typeorm_1 = require("typeorm");
const dotenv_1 = require("dotenv");
const admin_user_seeder_1 = require("./admin-user.seeder");
const fs = require("fs");
(0, dotenv_1.config)();
async function runSeeds() {
    console.log('🌱 Starting database seeding...');
    const dataSource = new typeorm_1.DataSource({
        type: 'postgres',
        host: process.env.DB_HOST || 'localhost',
        port: parseInt(process.env.DB_PORT, 10) || 5432,
        username: process.env.DB_USERNAME || 'invoice_user',
        password: process.env.DB_PASSWORD || 'invoice_password',
        database: process.env.DB_DATABASE || 'invoice_db',
        entities: [__dirname + '/../entities/*.entity{.ts,.js}'],
        synchronize: false,
        logging: false,
        ssl: process.env.DB_SSL_ENABLED === 'true'
            ? {
                rejectUnauthorized: process.env.DB_SSL_REJECT_UNAUTHORIZED !== 'false',
                ca: process.env.DB_SSL_CA_CERT_PATH
                    ? fs.readFileSync(process.env.DB_SSL_CA_CERT_PATH)
                    : undefined,
            }
            : false,
    });
    try {
        await dataSource.initialize();
        console.log('📦 Database connection established');
        const adminSeeder = new admin_user_seeder_1.AdminUserSeeder();
        await adminSeeder.run(dataSource);
        console.log('✅ Database seeding completed successfully');
    }
    catch (error) {
        console.error('❌ Error during seeding:', error);
        process.exit(1);
    }
    finally {
        if (dataSource.isInitialized) {
            await dataSource.destroy();
            console.log('📦 Database connection closed');
        }
    }
}
if (require.main === module) {
    runSeeds();
}
//# sourceMappingURL=run-seeds.js.map