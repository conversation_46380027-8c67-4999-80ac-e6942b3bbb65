declare const _default: (() => {
    nodeEnv: string;
    port: number;
    apiPrefix: string;
    corsOrigin: string;
    logLevel: string;
    rateLimitTtl: number;
    rateLimitLimit: number;
    maxFileSize: number;
    uploadDest: string;
}) & import("@nestjs/config").ConfigFactoryKeyHost<{
    nodeEnv: string;
    port: number;
    apiPrefix: string;
    corsOrigin: string;
    logLevel: string;
    rateLimitTtl: number;
    rateLimitLimit: number;
    maxFileSize: number;
    uploadDest: string;
}>;
export default _default;
