"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SinvoiceLoginResponseDto = exports.SinvoiceLoginDto = void 0;
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
class SinvoiceLoginDto {
    apiToken;
}
exports.SinvoiceLoginDto = SinvoiceLoginDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Customer API token for authentication',
        example: 'a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0u1v2w3x4y5z6a7b8c9d0e1f2',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], SinvoiceLoginDto.prototype, "apiToken", void 0);
class SinvoiceLoginResponseDto {
    accessToken;
    tokenType;
    refreshToken;
    expiresIn;
    scope;
    iat;
    invoiceCluster;
    type;
    jti;
    message;
}
exports.SinvoiceLoginResponseDto = SinvoiceLoginResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Sinvoice access token',
        example: 'eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...',
    }),
    __metadata("design:type", String)
], SinvoiceLoginResponseDto.prototype, "accessToken", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Token type',
        example: 'bearer',
    }),
    __metadata("design:type", String)
], SinvoiceLoginResponseDto.prototype, "tokenType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Sinvoice refresh token',
        example: 'eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...',
    }),
    __metadata("design:type", String)
], SinvoiceLoginResponseDto.prototype, "refreshToken", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Token expiration time in seconds',
        example: 1199,
    }),
    __metadata("design:type", Number)
], SinvoiceLoginResponseDto.prototype, "expiresIn", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Token scope',
        example: 'openid',
    }),
    __metadata("design:type", String)
], SinvoiceLoginResponseDto.prototype, "scope", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Token issued at timestamp',
        example: **********,
    }),
    __metadata("design:type", Number)
], SinvoiceLoginResponseDto.prototype, "iat", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Invoice cluster identifier',
        example: 'cluster7',
    }),
    __metadata("design:type", String)
], SinvoiceLoginResponseDto.prototype, "invoiceCluster", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Account type',
        example: 1,
    }),
    __metadata("design:type", Number)
], SinvoiceLoginResponseDto.prototype, "type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'JWT ID',
        example: '3d4a5f1f-80ab-4596-af34-24c78bc60e5b',
    }),
    __metadata("design:type", String)
], SinvoiceLoginResponseDto.prototype, "jti", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Success message',
        example: 'Sinvoice login successful',
    }),
    __metadata("design:type", String)
], SinvoiceLoginResponseDto.prototype, "message", void 0);
//# sourceMappingURL=sinvoice-login.dto.js.map