import { InvoiceService } from '../services/invoice.service';
import { CreateInvoiceDto } from '../dto/create-invoice.dto';
import { GetInvoicesDto } from '../dto/get-invoices.dto';
export declare class InvoiceController {
    private readonly invoiceService;
    constructor(invoiceService: InvoiceService);
    createInvoice(apiToken: string, createInvoiceDto: CreateInvoiceDto): Promise<any>;
    getInvoices(getInvoicesDto: GetInvoicesDto): Promise<any>;
}
