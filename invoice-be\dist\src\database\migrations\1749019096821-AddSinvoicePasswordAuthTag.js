"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddSinvoicePasswordAuthTag1749019096821 = void 0;
const typeorm_1 = require("typeorm");
class AddSinvoicePasswordAuthTag1749019096821 {
    name = 'AddSinvoicePasswordAuthTag1749019096821';
    async up(queryRunner) {
        await queryRunner.addColumn('customers', new typeorm_1.TableColumn({
            name: 'sinvoicePasswordAuthTag',
            type: 'varchar',
            length: '32',
            isNullable: true,
        }));
        await queryRunner.query(`CREATE INDEX "IDX_customers_sinvoicePasswordAuthTag" ON "customers" ("sinvoicePasswordAuthTag")`);
    }
    async down(queryRunner) {
        await queryRunner.query(`DROP INDEX IF EXISTS "IDX_customers_sinvoicePasswordAuthTag"`);
        await queryRunner.dropColumn('customers', 'sinvoicePasswordAuthTag');
    }
}
exports.AddSinvoicePasswordAuthTag1749019096821 = AddSinvoicePasswordAuthTag1749019096821;
//# sourceMappingURL=1749019096821-AddSinvoicePasswordAuthTag.js.map