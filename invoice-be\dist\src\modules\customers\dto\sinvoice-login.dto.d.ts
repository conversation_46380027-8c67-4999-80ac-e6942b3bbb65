export declare class SinvoiceLoginDto {
    apiToken: string;
}
export declare class SinvoiceLoginResponseDto {
    accessToken: string;
    tokenType: string;
    refreshToken: string;
    expiresIn: number;
    scope: string;
    iat: number;
    invoiceCluster: string;
    type: number;
    jti: string;
    message: string;
}
export interface SinvoiceApiLoginRequest {
    username: string;
    password: string;
}
export interface SinvoiceApiLoginResponse {
    access_token: string;
    token_type: string;
    refresh_token: string;
    expires_in: number;
    scope: string;
    iat: number;
    invoice_cluster: string;
    type: number;
    jti: string;
}
