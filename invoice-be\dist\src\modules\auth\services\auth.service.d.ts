import { JwtService } from '@nestjs/jwt';
import { Repository } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import { User } from '../../../database/entities/user.entity';
import { LoginDto, AuthResponseDto, UserInfoDto } from '../dto';
export declare class AuthService {
    private userRepository;
    private jwtService;
    private configService;
    private readonly logger;
    constructor(userRepository: Repository<User>, jwtService: JwtService, configService: ConfigService);
    login(loginDto: LoginDto): Promise<AuthResponseDto>;
    validateUserById(userId: string): Promise<User | null>;
    findUserByUsername(username: string): Promise<User | null>;
    getUserProfile(userId: string): Promise<UserInfoDto>;
    private mapToUserInfo;
    private getTokenExpirationTime;
}
