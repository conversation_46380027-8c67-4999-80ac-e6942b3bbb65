"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var SinvoiceService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.SinvoiceService = void 0;
const common_1 = require("@nestjs/common");
const axios_1 = require("@nestjs/axios");
const config_1 = require("@nestjs/config");
const rxjs_1 = require("rxjs");
const axios_2 = require("axios");
let SinvoiceService = SinvoiceService_1 = class SinvoiceService {
    httpService;
    configService;
    logger = new common_1.Logger(SinvoiceService_1.name);
    sinvoiceApiUrl;
    constructor(httpService, configService) {
        this.httpService = httpService;
        this.configService = configService;
        this.sinvoiceApiUrl = this.configService.get('SINVOICE_API_URL', process.env.SINVOICE_API_URL);
        if (!this.sinvoiceApiUrl) {
            throw new Error('SINVOICE_API_URL is not configured');
        }
        this.logger.log(`Sinvoice API URL configured: ${this.sinvoiceApiUrl}`);
    }
    async login(username, password) {
        const loginRequest = {
            username,
            password,
        };
        try {
            this.logger.log(`Attempting Sinvoice login for username: ${username}`);
            const response = await (0, rxjs_1.firstValueFrom)(this.httpService.post(`${this.sinvoiceApiUrl}/auth/login`, loginRequest, {
                timeout: 30000,
                headers: {
                    'Content-Type': 'application/json',
                    'User-Agent': 'Invoice-Service/1.0',
                },
            }));
            this.logger.log(`Sinvoice login successful for username: ${username}`);
            return response.data;
        }
        catch (error) {
            this.logger.error(`Sinvoice login failed for username: ${username}`, error instanceof axios_2.AxiosError ? error.message : error);
            if (error instanceof axios_2.AxiosError) {
                if (error.response?.status === 401) {
                    throw new common_1.UnauthorizedException('Invalid Sinvoice credentials');
                }
                if (error.response?.status === 400) {
                    throw new common_1.BadRequestException('Invalid login request format');
                }
                if (error.response?.status >= 500) {
                    throw new common_1.InternalServerErrorException('Sinvoice API server error');
                }
                if (error.code === 'ECONNABORTED') {
                    throw new common_1.InternalServerErrorException('Sinvoice API timeout');
                }
                if (error.code === 'ENOTFOUND') {
                    throw new common_1.InternalServerErrorException(`Sinvoice API DNS resolution failed. Please check the API URL: ${this.sinvoiceApiUrl}`);
                }
                if (error.code === 'ECONNREFUSED') {
                    throw new common_1.InternalServerErrorException('Sinvoice API connection refused. The service may be down.');
                }
                if (error.code === 'ETIMEDOUT') {
                    throw new common_1.InternalServerErrorException('Sinvoice API connection timeout. Please try again later.');
                }
            }
            throw new common_1.InternalServerErrorException('Sinvoice API connection failed');
        }
    }
    async refreshToken(refreshToken) {
        try {
            this.logger.log('Attempting to refresh Sinvoice token');
            const response = await (0, rxjs_1.firstValueFrom)(this.httpService.post(`${this.sinvoiceApiUrl}/auth/refresh`, { refresh_token: refreshToken }, {
                timeout: 30000,
                headers: {
                    'Content-Type': 'application/json',
                    'User-Agent': 'Invoice-Service/1.0',
                },
            }));
            this.logger.log('Sinvoice token refresh successful');
            return response.data;
        }
        catch (error) {
            this.logger.error('Sinvoice token refresh failed', error instanceof axios_2.AxiosError ? error.message : error);
            if (error instanceof axios_2.AxiosError) {
                if (error.response?.status === 401) {
                    throw new common_1.UnauthorizedException('Invalid or expired refresh token');
                }
                if (error.response?.status >= 500) {
                    throw new common_1.InternalServerErrorException('Sinvoice API server error');
                }
                if (error.code === 'ECONNABORTED') {
                    throw new common_1.InternalServerErrorException('Sinvoice API timeout');
                }
                if (error.code === 'ENOTFOUND') {
                    throw new common_1.InternalServerErrorException(`Sinvoice API DNS resolution failed. Please check the API URL: ${this.sinvoiceApiUrl}`);
                }
                if (error.code === 'ECONNREFUSED') {
                    throw new common_1.InternalServerErrorException('Sinvoice API connection refused. The service may be down.');
                }
                if (error.code === 'ETIMEDOUT') {
                    throw new common_1.InternalServerErrorException('Sinvoice API connection timeout. Please try again later.');
                }
            }
            throw new common_1.InternalServerErrorException('Sinvoice token refresh failed');
        }
    }
    async validateAccessToken(accessToken) {
        try {
            await (0, rxjs_1.firstValueFrom)(this.httpService.get(`${this.sinvoiceApiUrl}/auth/validate`, {
                timeout: 10000,
                headers: {
                    Authorization: `Bearer ${accessToken}`,
                    'User-Agent': 'Invoice-Service/1.0',
                },
            }));
            return true;
        }
        catch (error) {
            this.logger.warn('Sinvoice access token validation failed');
            return false;
        }
    }
};
exports.SinvoiceService = SinvoiceService;
exports.SinvoiceService = SinvoiceService = SinvoiceService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [axios_1.HttpService,
        config_1.ConfigService])
], SinvoiceService);
//# sourceMappingURL=sinvoice.service.js.map