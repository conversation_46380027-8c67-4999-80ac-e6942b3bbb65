"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const typeorm_1 = require("typeorm");
const dotenv_1 = require("dotenv");
const fs = require("fs");
const path = require("path");
(0, dotenv_1.config)();
function createTypeOrmSSLConfig() {
    const sslEnabled = process.env.DB_SSL_ENABLED === 'true';
    if (!sslEnabled) {
        return false;
    }
    const sslConfig = {
        rejectUnauthorized: process.env.DB_SSL_REJECT_UNAUTHORIZED !== 'false',
    };
    if (process.env.DB_SSL_CA_CERT_PATH) {
        const caPath = path.resolve(process.env.DB_SSL_CA_CERT_PATH);
        if (fs.existsSync(caPath)) {
            sslConfig.ca = fs.readFileSync(caPath);
        }
    }
    if (process.env.DB_SSL_CLIENT_CERT_PATH) {
        const certPath = path.resolve(process.env.DB_SSL_CLIENT_CERT_PATH);
        if (fs.existsSync(certPath)) {
            sslConfig.cert = fs.readFileSync(certPath);
        }
    }
    if (process.env.DB_SSL_CLIENT_KEY_PATH) {
        const keyPath = path.resolve(process.env.DB_SSL_CLIENT_KEY_PATH);
        if (fs.existsSync(keyPath)) {
            sslConfig.key = fs.readFileSync(keyPath);
        }
    }
    if (process.env.DB_SSL_MODE) {
        sslConfig.sslmode = process.env.DB_SSL_MODE;
    }
    return sslConfig;
}
exports.default = new typeorm_1.DataSource({
    type: 'postgres',
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT, 10) || 5432,
    username: process.env.DB_USERNAME || 'invoice_user',
    password: process.env.DB_PASSWORD || 'invoice_password',
    database: process.env.DB_DATABASE || 'invoice_db',
    entities: ['src/database/entities/*.entity.ts'],
    migrations: ['src/database/migrations/*.ts'],
    synchronize: false,
    logging: process.env.NODE_ENV === 'development',
    ssl: createTypeOrmSSLConfig(),
});
//# sourceMappingURL=typeorm.config.js.map