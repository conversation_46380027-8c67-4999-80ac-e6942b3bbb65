"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateUserRoleToCustomer1749019096820 = void 0;
class UpdateUserRoleToCustomer1749019096820 {
    async up(queryRunner) {
        await queryRunner.query(`
            ALTER TABLE users
            ALTER COLUMN role DROP DEFAULT
        `);
        await queryRunner.query(`
            ALTER TYPE users_role_enum RENAME TO users_role_enum_old
        `);
        await queryRunner.query(`
            CREATE TYPE users_role_enum AS ENUM ('admin', 'user', 'customer')
        `);
        await queryRunner.query(`
            ALTER TABLE users
            ALTER COLUMN role TYPE users_role_enum
            USING role::text::users_role_enum
        `);
        await queryRunner.query(`
            DROP TYPE users_role_enum_old
        `);
        await queryRunner.query(`
            UPDATE users
            SET role = 'customer'
            WHERE role = 'user'
        `);
        await queryRunner.query(`
            ALTER TYPE users_role_enum RENAME TO users_role_enum_old
        `);
        await queryRunner.query(`
            CREATE TYPE users_role_enum AS ENUM ('admin', 'customer')
        `);
        await queryRunner.query(`
            ALTER TABLE users
            ALTER COLUMN role TYPE users_role_enum
            USING role::text::users_role_enum
        `);
        await queryRunner.query(`
            DROP TYPE users_role_enum_old
        `);
        await queryRunner.query(`
            ALTER TABLE users
            ALTER COLUMN role SET DEFAULT 'customer'
        `);
    }
    async down(queryRunner) {
        await queryRunner.query(`
            UPDATE users
            SET role = 'user'
            WHERE role = 'customer'
        `);
        await queryRunner.query(`
            ALTER TYPE users_role_enum RENAME TO users_role_enum_old
        `);
        await queryRunner.query(`
            CREATE TYPE users_role_enum AS ENUM ('admin', 'user')
        `);
        await queryRunner.query(`
            ALTER TABLE users
            ALTER COLUMN role TYPE users_role_enum
            USING role::text::users_role_enum
        `);
        await queryRunner.query(`
            DROP TYPE users_role_enum_old
        `);
    }
}
exports.UpdateUserRoleToCustomer1749019096820 = UpdateUserRoleToCustomer1749019096820;
//# sourceMappingURL=1749019096820-UpdateUserRoleToCustomer.js.map