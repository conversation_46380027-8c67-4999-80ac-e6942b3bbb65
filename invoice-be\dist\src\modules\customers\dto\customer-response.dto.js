"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CustomerResponseDto = exports.CustomerUserDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const user_entity_1 = require("../../../database/entities/user.entity");
class CustomerUserDto {
    id;
    username;
    email;
    role;
    isActive;
    lastLoginAt;
    createdAt;
    updatedAt;
}
exports.CustomerUserDto = CustomerUserDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'User ID',
        example: '123e4567-e89b-12d3-a456-************',
    }),
    __metadata("design:type", String)
], CustomerUserDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Username',
        example: 'customer123',
    }),
    __metadata("design:type", String)
], CustomerUserDto.prototype, "username", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Email address',
        example: '<EMAIL>',
    }),
    __metadata("design:type", String)
], CustomerUserDto.prototype, "email", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'User role',
        enum: user_entity_1.UserRole,
        example: user_entity_1.UserRole.CUSTOMER,
    }),
    __metadata("design:type", String)
], CustomerUserDto.prototype, "role", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Account status',
        example: true,
    }),
    __metadata("design:type", Boolean)
], CustomerUserDto.prototype, "isActive", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Last login timestamp',
        example: '2023-12-01T10:00:00.000Z',
        nullable: true,
    }),
    __metadata("design:type", Date)
], CustomerUserDto.prototype, "lastLoginAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Account creation timestamp',
        example: '2023-12-01T10:00:00.000Z',
    }),
    __metadata("design:type", Date)
], CustomerUserDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Account last update timestamp',
        example: '2023-12-01T10:00:00.000Z',
    }),
    __metadata("design:type", Date)
], CustomerUserDto.prototype, "updatedAt", void 0);
class CustomerResponseDto {
    id;
    user;
    sinvoiceUsername;
    apiToken;
    hasSinvoiceTokens;
    sinvoiceTokenExpiresAt;
    createdAt;
    updatedAt;
}
exports.CustomerResponseDto = CustomerResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Customer ID',
        example: '123e4567-e89b-12d3-a456-************',
    }),
    __metadata("design:type", String)
], CustomerResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'User information',
        type: CustomerUserDto,
    }),
    __metadata("design:type", CustomerUserDto)
], CustomerResponseDto.prototype, "user", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Sinvoice username',
        example: 'sinvoice_user',
    }),
    __metadata("design:type", String)
], CustomerResponseDto.prototype, "sinvoiceUsername", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'API token for accessing services',
        example: 'abc123def456...',
    }),
    __metadata("design:type", String)
], CustomerResponseDto.prototype, "apiToken", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Whether Sinvoice tokens are active',
        example: false,
    }),
    __metadata("design:type", Boolean)
], CustomerResponseDto.prototype, "hasSinvoiceTokens", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Sinvoice token expiration timestamp',
        example: '2023-12-01T10:00:00.000Z',
        nullable: true,
    }),
    __metadata("design:type", Date)
], CustomerResponseDto.prototype, "sinvoiceTokenExpiresAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Customer creation timestamp',
        example: '2023-12-01T10:00:00.000Z',
    }),
    __metadata("design:type", Date)
], CustomerResponseDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Customer last update timestamp',
        example: '2023-12-01T10:00:00.000Z',
    }),
    __metadata("design:type", Date)
], CustomerResponseDto.prototype, "updatedAt", void 0);
//# sourceMappingURL=customer-response.dto.js.map