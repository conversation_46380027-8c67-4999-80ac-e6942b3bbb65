{"version": 3, "file": "1703000000000-CreateUserTable.js", "sourceRoot": "", "sources": ["../../../../src/database/migrations/1703000000000-CreateUserTable.ts"], "names": [], "mappings": ";;;AAAA,qCAAiE;AAEjE,MAAa,4BAA4B;IACvC,IAAI,GAAG,8BAA8B,CAAC;IAE/B,KAAK,CAAC,EAAE,CAAC,WAAwB;QAEtC,MAAM,WAAW,CAAC,WAAW,CAC3B,IAAI,eAAK,CAAC;YACR,IAAI,EAAE,OAAO;YACb,OAAO,EAAE;gBACP;oBACE,IAAI,EAAE,IAAI;oBACV,IAAI,EAAE,MAAM;oBACZ,SAAS,EAAE,IAAI;oBACf,kBAAkB,EAAE,MAAM;oBAC1B,OAAO,EAAE,oBAAoB;iBAC9B;gBACD;oBACE,IAAI,EAAE,UAAU;oBAChB,IAAI,EAAE,SAAS;oBACf,MAAM,EAAE,IAAI;oBACZ,QAAQ,EAAE,IAAI;oBACd,UAAU,EAAE,KAAK;iBAClB;gBACD;oBACE,IAAI,EAAE,OAAO;oBACb,IAAI,EAAE,SAAS;oBACf,MAAM,EAAE,KAAK;oBACb,QAAQ,EAAE,IAAI;oBACd,UAAU,EAAE,KAAK;iBAClB;gBACD;oBACE,IAAI,EAAE,UAAU;oBAChB,IAAI,EAAE,SAAS;oBACf,MAAM,EAAE,KAAK;oBACb,UAAU,EAAE,KAAK;iBAClB;gBACD;oBACE,IAAI,EAAE,MAAM;oBACZ,IAAI,EAAE,MAAM;oBACZ,IAAI,EAAE,CAAC,OAAO,EAAE,MAAM,CAAC;oBACvB,OAAO,EAAE,QAAQ;oBACjB,UAAU,EAAE,KAAK;iBAClB;gBACD;oBACE,IAAI,EAAE,UAAU;oBAChB,IAAI,EAAE,SAAS;oBACf,OAAO,EAAE,IAAI;oBACb,UAAU,EAAE,KAAK;iBAClB;gBACD;oBACE,IAAI,EAAE,aAAa;oBACnB,IAAI,EAAE,WAAW;oBACjB,UAAU,EAAE,IAAI;iBACjB;gBACD;oBACE,IAAI,EAAE,WAAW;oBACjB,IAAI,EAAE,WAAW;oBACjB,OAAO,EAAE,mBAAmB;oBAC5B,UAAU,EAAE,KAAK;iBAClB;gBACD;oBACE,IAAI,EAAE,WAAW;oBACjB,IAAI,EAAE,WAAW;oBACjB,OAAO,EAAE,mBAAmB;oBAC5B,QAAQ,EAAE,mBAAmB;oBAC7B,UAAU,EAAE,KAAK;iBAClB;aACF;SACF,CAAC,EACF,IAAI,CACL,CAAC;QAGF,MAAM,WAAW,CAAC,KAAK,CACrB,mDAAmD,CACpD,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACrB,2DAA2D,CAC5D,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QAExC,MAAM,WAAW,CAAC,KAAK,CAAC,2CAA2C,CAAC,CAAC;QACrE,MAAM,WAAW,CAAC,KAAK,CAAC,uCAAuC,CAAC,CAAC;QAGjE,MAAM,WAAW,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;IACvC,CAAC;CACF;AAzFD,oEAyFC"}