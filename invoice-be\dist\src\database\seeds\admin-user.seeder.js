"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdminUserSeeder = void 0;
const user_entity_1 = require("../entities/user.entity");
class AdminUserSeeder {
    async run(dataSource) {
        const userRepository = dataSource.getRepository(user_entity_1.User);
        const existingAdmin = await userRepository.findOne({
            where: { role: user_entity_1.UserRole.ADMIN },
        });
        if (existingAdmin) {
            console.log('Admin user already exists, skipping seeder');
            return;
        }
        const adminUsername = process.env.ADMIN_USERNAME || 'admin';
        const adminEmail = process.env.ADMIN_EMAIL || '<EMAIL>';
        const adminPassword = process.env.ADMIN_PASSWORD || 'admin123';
        const existingUser = await userRepository.findOne({
            where: [{ username: adminUsername }, { email: adminEmail }],
        });
        if (existingUser) {
            console.log(`User with username '${adminUsername}' or email '${adminEmail}' already exists`);
            return;
        }
        const adminUser = new user_entity_1.User();
        adminUser.username = adminUsername;
        adminUser.email = adminEmail;
        adminUser.password = adminPassword;
        adminUser.role = user_entity_1.UserRole.ADMIN;
        adminUser.isActive = true;
        try {
            await userRepository.save(adminUser);
            console.log(`✅ Admin user created successfully:`);
            console.log(`   Username: ${adminUsername}`);
            console.log(`   Email: ${adminEmail}`);
            console.log(`   Password: ${adminPassword}`);
            console.log(`   Role: ${user_entity_1.UserRole.ADMIN}`);
            console.log('');
            console.log('⚠️  IMPORTANT: Change the default admin password after first login!');
        }
        catch (error) {
            console.error('❌ Error creating admin user:', error);
            throw error;
        }
    }
}
exports.AdminUserSeeder = AdminUserSeeder;
//# sourceMappingURL=admin-user.seeder.js.map