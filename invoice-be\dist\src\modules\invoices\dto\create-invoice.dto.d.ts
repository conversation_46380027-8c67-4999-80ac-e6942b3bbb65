export declare class GeneralInvoiceInfoDto {
    invoiceType: string;
    templateCode: string;
    invoiceSeries: string;
    currencyCode: string;
    adjustmentType: string;
    paymentStatus: boolean;
    cusGetInvoiceRight: boolean;
}
export declare class SellerInfoDto {
    sellerLegalName: string;
    sellerTaxCode: string;
    sellerAddressLine: string;
    sellerPhoneNumber: string;
    sellerFaxNumber: string;
    sellerEmail: string;
    sellerBankName: string;
    sellerBankAccount: string;
    sellerDistrictName: string;
    sellerCityName: string;
    sellerCountryCode: string;
    sellerWebsite: string;
}
export declare class BuyerInfoDto {
    buyerName: string;
    buyerLegalName: string;
    buyerTaxCode: string;
    buyerAddressLine: string;
    buyerPostalCode: string;
    buyerDistrictName: string;
    buyerCityName: string;
    buyerCountryCode: string;
    buyerPhoneNumber: string;
    buyerFaxNumber: string;
    buyerEmail: string;
    buyerBankName: string;
    buyerBankAccount: string;
    buyerIdType: string;
    buyerIdNo: string;
    buyerCode: string;
    buyerBirthDay: string;
}
export declare class PaymentDto {
    paymentMethodName: string;
}
export declare class TaxBreakdownDto {
    taxPercentage: number;
    taxableAmount: number;
    taxAmount: number;
}
export declare class ItemInfoDto {
    lineNumber: number;
    itemCode: string;
    itemName: string;
    unitName: string;
    unitPrice: number;
    quantity: number;
    selection: number;
    itemTotalAmountWithoutTax: number;
    taxPercentage: number;
    taxAmount: number;
    discount: number | null;
    discount2: number | null;
    itemDiscount: number;
    itemNote: string | null;
    batchNo: string | null;
    expDate: string | null;
}
export declare class SummarizeInfoDto {
    extraName: string;
    extraValue: string;
}
export declare class CreateInvoiceDto {
    generalInvoiceInfo: GeneralInvoiceInfoDto;
    sellerInfo: SellerInfoDto;
    buyerInfo: BuyerInfoDto;
    payments: PaymentDto[];
    taxBreakdowns: TaxBreakdownDto[];
    itemInfo: ItemInfoDto[];
    summarizeInfo: SummarizeInfoDto;
}
