"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SSLUtil = void 0;
const fs = require("fs");
const path = require("path");
const common_1 = require("@nestjs/common");
const logger = new common_1.Logger('SSLUtil');
class SSLUtil {
    static validateCertificateFiles(certPaths) {
        try {
            for (const [type, certPath] of Object.entries(certPaths)) {
                if (certPath) {
                    const resolvedPath = path.resolve(certPath);
                    if (!fs.existsSync(resolvedPath)) {
                        logger.error(`SSL ${type} certificate file not found: ${resolvedPath}`);
                        return false;
                    }
                    fs.accessSync(resolvedPath, fs.constants.R_OK);
                    logger.log(`SSL ${type} certificate validated: ${resolvedPath}`);
                }
            }
            return true;
        }
        catch (error) {
            logger.error('SSL certificate validation failed:', error.message);
            return false;
        }
    }
    static createCloudSSLConfig(provider) {
        const baseConfig = {
            rejectUnauthorized: false,
        };
        switch (provider) {
            case 'aws':
                return {
                    ...baseConfig,
                    sslmode: 'require',
                };
            case 'gcp':
                return {
                    ...baseConfig,
                    sslmode: 'require',
                };
            case 'azure':
                return {
                    ...baseConfig,
                    sslmode: 'require',
                };
            case 'custom':
                return {
                    rejectUnauthorized: true,
                    sslmode: 'verify-full',
                };
            default:
                return baseConfig;
        }
    }
    static logSSLInfo(sslConfig) {
        if (!sslConfig) {
            logger.log('Database SSL: Disabled');
            return;
        }
        logger.log('Database SSL: Enabled');
        logger.log(`SSL Reject Unauthorized: ${sslConfig.rejectUnauthorized}`);
        if (sslConfig.sslmode) {
            logger.log(`SSL Mode: ${sslConfig.sslmode}`);
        }
        if (sslConfig.ca) {
            logger.log('SSL CA Certificate: Loaded');
        }
        if (sslConfig.cert) {
            logger.log('SSL Client Certificate: Loaded');
        }
        if (sslConfig.key) {
            logger.log('SSL Client Key: Loaded');
        }
    }
    static createEnvironmentSSLConfig() {
        const nodeEnv = process.env.NODE_ENV;
        const sslEnabled = process.env.DB_SSL_ENABLED === 'true';
        if (!sslEnabled) {
            return false;
        }
        if (nodeEnv === 'production') {
            const cloudProvider = process.env.DB_CLOUD_PROVIDER;
            if (cloudProvider) {
                return this.createCloudSSLConfig(cloudProvider);
            }
            return {
                rejectUnauthorized: process.env.DB_SSL_REJECT_UNAUTHORIZED !== 'false',
                sslmode: process.env.DB_SSL_MODE || 'require',
            };
        }
        return {
            rejectUnauthorized: process.env.DB_SSL_REJECT_UNAUTHORIZED === 'true',
            sslmode: process.env.DB_SSL_MODE || 'prefer',
        };
    }
}
exports.SSLUtil = SSLUtil;
//# sourceMappingURL=ssl.util.js.map