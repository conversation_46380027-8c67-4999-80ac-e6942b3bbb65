import { UserRole } from '../../../database/entities/user.entity';
export declare class CustomerUserDto {
    id: string;
    username: string;
    email: string;
    role: UserRole;
    isActive: boolean;
    lastLoginAt: Date | null;
    createdAt: Date;
    updatedAt: Date;
}
export declare class CustomerResponseDto {
    id: string;
    user: CustomerUserDto;
    sinvoiceUsername: string;
    apiToken: string;
    hasSinvoiceTokens: boolean;
    sinvoiceTokenExpiresAt: Date | null;
    createdAt: Date;
    updatedAt: Date;
}
