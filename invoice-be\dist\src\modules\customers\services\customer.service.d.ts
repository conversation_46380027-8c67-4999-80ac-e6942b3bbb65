import { Repository, DataSource } from 'typeorm';
import { Customer } from '../../../database/entities/customer.entity';
import { User } from '../../../database/entities/user.entity';
import { CreateCustomerDto, CustomerResponseDto, UpdateCustomerDto, ApiTokenResponseDto, SinvoiceLoginResponseDto } from '../dto';
import { SinvoiceService } from './sinvoice.service';
export declare class CustomerService {
    private customerRepository;
    private userRepository;
    private dataSource;
    private sinvoiceService;
    private readonly logger;
    constructor(customerRepository: Repository<Customer>, userRepository: Repository<User>, dataSource: DataSource, sinvoiceService: SinvoiceService);
    createCustomer(createCustomerDto: CreateCustomerDto): Promise<CustomerResponseDto>;
    getCustomerById(id: string): Promise<CustomerResponseDto>;
    getCustomerByUserId(userId: string): Promise<CustomerResponseDto>;
    getCustomerByApiToken(apiToken: string): Promise<Customer | null>;
    updateCustomer(id: string, updateCustomerDto: UpdateCustomerDto): Promise<CustomerResponseDto>;
    resetApiToken(id: string): Promise<ApiTokenResponseDto>;
    updateSinvoiceTokens(id: string, accessToken: string, refreshToken: string, expiresIn: number): Promise<void>;
    getAllCustomers(): Promise<CustomerResponseDto[]>;
    sinvoiceLogin(apiToken: string): Promise<SinvoiceLoginResponseDto>;
    refreshSinvoiceToken(customerId: string): Promise<SinvoiceLoginResponseDto>;
    deleteCustomer(id: string): Promise<void>;
    private mapToCustomerResponse;
}
