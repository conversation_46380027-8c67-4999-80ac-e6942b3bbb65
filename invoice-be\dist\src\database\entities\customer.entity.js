"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var Customer_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.Customer = void 0;
const typeorm_1 = require("typeorm");
const class_transformer_1 = require("class-transformer");
const crypto = require("crypto");
const user_entity_1 = require("./user.entity");
let Customer = Customer_1 = class Customer {
    id;
    userId;
    user;
    sinvoiceUsername;
    sinvoicePasswordEncrypted;
    sinvoicePasswordIv;
    sinvoicePasswordAuthTag;
    apiToken;
    sinvoiceAccessToken;
    sinvoiceRefreshToken;
    sinvoiceTokenExpiresAt;
    createdAt;
    updatedAt;
    generateApiToken() {
        if (!this.apiToken) {
            this.apiToken = this.generateSecureToken();
        }
    }
    generateSecureToken() {
        return crypto.randomBytes(32).toString('hex');
    }
    regenerateApiToken() {
        this.apiToken = this.generateSecureToken();
    }
    static encryptSinvoicePassword(password) {
        const algorithm = 'aes-256-gcm';
        const key = crypto.scryptSync(process.env.ENCRYPTION_KEY ||
            'default-encryption-key-change-in-production', 'salt', 32);
        const iv = crypto.randomBytes(16);
        const cipher = crypto.createCipheriv(algorithm, key, iv);
        let encrypted = cipher.update(password, 'utf8', 'hex');
        encrypted += cipher.final('hex');
        const authTag = cipher.getAuthTag();
        return {
            encrypted,
            iv: iv.toString('hex'),
            authTag: authTag.toString('hex'),
        };
    }
    static decryptSinvoicePassword(encrypted, iv, authTag) {
        const key = crypto.scryptSync(process.env.ENCRYPTION_KEY ||
            'default-encryption-key-change-in-production', 'salt', 32);
        const ivBuffer = Buffer.from(iv, 'hex');
        try {
            if (authTag) {
                const algorithm = 'aes-256-gcm';
                const decipher = crypto.createDecipheriv(algorithm, key, ivBuffer);
                decipher.setAuthTag(Buffer.from(authTag, 'hex'));
                let decrypted = decipher.update(encrypted, 'hex', 'utf8');
                decrypted += decipher.final('utf8');
                return decrypted;
            }
            else {
                try {
                    const gcmAlgorithm = 'aes-256-gcm';
                    const gcmDecipher = crypto.createDecipheriv(gcmAlgorithm, key, ivBuffer);
                    let decrypted = gcmDecipher.update(encrypted, 'hex', 'utf8');
                    decrypted += gcmDecipher.final('utf8');
                    return decrypted;
                }
                catch (gcmError) {
                    try {
                        const cbcAlgorithm = 'aes-256-cbc';
                        const cbcDecipher = crypto.createDecipheriv(cbcAlgorithm, key, ivBuffer);
                        let decrypted = cbcDecipher.update(encrypted, 'hex', 'utf8');
                        decrypted += cbcDecipher.final('utf8');
                        return decrypted;
                    }
                    catch (cbcError) {
                        throw new Error(`Failed to decrypt legacy password with both GCM and CBC: GCM error: ${gcmError instanceof Error ? gcmError.message : 'Unknown'}, CBC error: ${cbcError instanceof Error ? cbcError.message : 'Unknown'}`);
                    }
                }
            }
        }
        catch (error) {
            throw new Error(`Failed to decrypt Sinvoice password: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    getSinvoicePassword() {
        return Customer_1.decryptSinvoicePassword(this.sinvoicePasswordEncrypted, this.sinvoicePasswordIv, this.sinvoicePasswordAuthTag);
    }
    setSinvoicePassword(password) {
        const { encrypted, iv, authTag } = Customer_1.encryptSinvoicePassword(password);
        this.sinvoicePasswordEncrypted = encrypted;
        this.sinvoicePasswordIv = iv;
        this.sinvoicePasswordAuthTag = authTag;
    }
    migrateLegacyPassword() {
        if (this.sinvoicePasswordAuthTag) {
            return;
        }
        try {
            const decryptedPassword = Customer_1.decryptSinvoicePassword(this.sinvoicePasswordEncrypted, this.sinvoicePasswordIv, null);
            this.setSinvoicePassword(decryptedPassword);
        }
        catch (error) {
            throw new Error(`Failed to migrate legacy password: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    updateSinvoiceTokens(accessToken, refreshToken, expiresIn) {
        this.sinvoiceAccessToken = accessToken;
        this.sinvoiceRefreshToken = refreshToken;
        this.sinvoiceTokenExpiresAt = new Date(Date.now() + expiresIn * 1000);
    }
    isSinvoiceTokenExpired() {
        if (!this.sinvoiceTokenExpiresAt) {
            return true;
        }
        return new Date() >= this.sinvoiceTokenExpiresAt;
    }
    clearSinvoiceTokens() {
        this.sinvoiceAccessToken = null;
        this.sinvoiceRefreshToken = null;
        this.sinvoiceTokenExpiresAt = null;
    }
    toJSON() {
        const { sinvoicePasswordEncrypted, sinvoicePasswordIv, sinvoiceAccessToken, sinvoiceRefreshToken, ...result } = this;
        return result;
    }
};
exports.Customer = Customer;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], Customer.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'uuid' }),
    __metadata("design:type", String)
], Customer.prototype, "userId", void 0);
__decorate([
    (0, typeorm_1.OneToOne)(() => user_entity_1.User, { eager: true, onDelete: 'CASCADE' }),
    (0, typeorm_1.JoinColumn)({ name: 'userId' }),
    __metadata("design:type", user_entity_1.User)
], Customer.prototype, "user", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 255 }),
    (0, class_transformer_1.Exclude)({ toPlainOnly: true }),
    __metadata("design:type", String)
], Customer.prototype, "sinvoiceUsername", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text' }),
    (0, class_transformer_1.Exclude)({ toPlainOnly: true }),
    __metadata("design:type", String)
], Customer.prototype, "sinvoicePasswordEncrypted", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 32 }),
    (0, class_transformer_1.Exclude)({ toPlainOnly: true }),
    __metadata("design:type", String)
], Customer.prototype, "sinvoicePasswordIv", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 32, nullable: true }),
    (0, class_transformer_1.Exclude)({ toPlainOnly: true }),
    __metadata("design:type", String)
], Customer.prototype, "sinvoicePasswordAuthTag", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 64, unique: true }),
    __metadata("design:type", String)
], Customer.prototype, "apiToken", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    (0, class_transformer_1.Exclude)({ toPlainOnly: true }),
    __metadata("design:type", String)
], Customer.prototype, "sinvoiceAccessToken", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    (0, class_transformer_1.Exclude)({ toPlainOnly: true }),
    __metadata("design:type", String)
], Customer.prototype, "sinvoiceRefreshToken", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp', nullable: true }),
    __metadata("design:type", Date)
], Customer.prototype, "sinvoiceTokenExpiresAt", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], Customer.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], Customer.prototype, "updatedAt", void 0);
__decorate([
    (0, typeorm_1.BeforeInsert)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], Customer.prototype, "generateApiToken", null);
exports.Customer = Customer = Customer_1 = __decorate([
    (0, typeorm_1.Entity)('customers')
], Customer);
//# sourceMappingURL=customer.entity.js.map