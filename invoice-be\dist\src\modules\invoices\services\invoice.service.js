"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var InvoiceService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.InvoiceService = void 0;
const common_1 = require("@nestjs/common");
const axios_1 = require("@nestjs/axios");
const config_1 = require("@nestjs/config");
const rxjs_1 = require("rxjs");
const axios_2 = require("axios");
const customer_service_1 = require("../../customers/services/customer.service");
let InvoiceService = InvoiceService_1 = class InvoiceService {
    httpService;
    configService;
    customerService;
    logger = new common_1.Logger(InvoiceService_1.name);
    sinvoiceApiUrl;
    errorMessages = {
        VAT_AMOUNT_INVALID: 'Invalid VAT amount. Please check the tax calculations.',
        VAT_TAX_AMOUNT_NEGATE: 'VAT tax amount cannot be negative.',
        VAT_PERCENTAGE_INVALID: 'Invalid VAT percentage. Please check the tax rate.',
        INVOICE_SERIAL_NOT_FOUND: 'Invalid invoice serial number. Please check the invoice series.',
        IVI_TOTAL_A_WITHOUT_TAX_AND_UP_QUAN_NOT_COMPARED: 'Unit price and total amount mismatch. Please check the calculations.',
        JSON_PARSE_ERROR: 'Invalid data format. Please check the input data.',
        BAD_REQUEST_INVOICE_NOT_USE_OTHER_FEE: 'Other fees are not allowed for this invoice type.',
        BAD_REQUEST_INVALID_DECIMAL_POINT_QUANTUM: 'Invalid decimal point configuration.',
        BAD_REQUEST_EXISTS_OTHER_USB_SIGN_PROCESSING: 'There are unsigned invoices being processed. Please wait.',
        GENERAL: 'Session expired. Please login again.',
        DEFAULT: 'An error occurred while processing the invoice. Please try again.',
    };
    constructor(httpService, configService, customerService) {
        this.httpService = httpService;
        this.configService = configService;
        this.customerService = customerService;
        this.sinvoiceApiUrl = this.configService.get('SINVOICE_API_URL');
        if (!this.sinvoiceApiUrl) {
            throw new Error('SINVOICE_API_URL is not configured');
        }
    }
    handleSinvoiceError(error) {
        const errorResponse = error.response?.data;
        const statusCode = error.response?.status;
        this.logger.error('Sinvoice API Error Details', {
            statusCode,
            errorResponse,
            originalError: error.message,
        });
        if (statusCode === 400) {
            const errorCode = errorResponse?.message || 'DEFAULT';
            const errorMessage = this.errorMessages[errorCode] || this.errorMessages.DEFAULT;
            const detailMessage = errorResponse?.data;
            throw new common_1.BadRequestException({
                statusCode: 400,
                message: errorMessage,
                error: 'Bad Request',
                details: detailMessage,
                code: errorCode,
            });
        }
        if (statusCode === 401) {
            throw new common_1.UnauthorizedException({
                statusCode: 401,
                message: 'Invalid Sinvoice credentials',
                error: 'Unauthorized',
            });
        }
        if (statusCode === 500) {
            if (errorResponse?.message === 'GENERAL') {
                throw new common_1.UnauthorizedException({
                    statusCode: 401,
                    message: this.errorMessages.GENERAL,
                    error: 'Session Expired',
                });
            }
            throw new common_1.InternalServerErrorException({
                statusCode: 500,
                message: 'Sinvoice service is currently unavailable',
                error: 'Internal Server Error',
                details: errorResponse?.message,
            });
        }
        throw new common_1.InternalServerErrorException({
            statusCode: 500,
            message: this.errorMessages.DEFAULT,
            error: 'Internal Server Error',
        });
    }
    async createInvoice(apiToken, createInvoiceDto) {
        const customer = await this.customerService.getCustomerByApiToken(apiToken);
        if (!customer) {
            throw new common_1.UnauthorizedException('Invalid API token');
        }
        try {
            const sinvoiceUsername = customer.sinvoiceUsername;
            const sinvoicePassword = customer.getSinvoicePassword();
            const authHeader = Buffer.from(`${sinvoiceUsername}:${sinvoicePassword}`).toString('base64');
            const response = await (0, rxjs_1.firstValueFrom)(this.httpService.post(`${this.sinvoiceApiUrl}/services/einvoiceapplication/api/InvoiceAPI/InvoiceWS/createInvoice/${sinvoiceUsername}`, createInvoiceDto, {
                headers: {
                    Authorization: `Basic ${authHeader}`,
                    'Content-Type': 'application/json',
                    'User-Agent': 'Invoice-Service/1.0',
                },
                timeout: 30000,
            }));
            return response.data;
        }
        catch (error) {
            if (error instanceof axios_2.AxiosError) {
                return this.handleSinvoiceError(error);
            }
            this.logger.error(`Unexpected error creating invoice for customer: ${customer.user.username}`, {
                error: error instanceof Error ? error.stack : error,
            });
            throw new common_1.InternalServerErrorException({
                statusCode: 500,
                message: this.errorMessages.DEFAULT,
                error: 'Internal Server Error',
            });
        }
    }
    async getInvoices(apiToken, searchParams) {
        const customer = await this.customerService.getCustomerByApiToken(apiToken);
        if (!customer) {
            throw new common_1.UnauthorizedException('Invalid API token');
        }
        try {
            const sinvoiceUsername = customer.sinvoiceUsername;
            const sinvoicePassword = customer.getSinvoicePassword();
            const authHeader = Buffer.from(`${sinvoiceUsername}:${sinvoicePassword}`).toString('base64');
            const response = await (0, rxjs_1.firstValueFrom)(this.httpService.post(`${this.sinvoiceApiUrl}/services/einvoiceapplication/api/InvoiceAPI/InvoiceUtilsWS/getInvoices/${sinvoiceUsername}`, searchParams, {
                headers: {
                    Authorization: `Basic ${authHeader}`,
                    'Content-Type': 'application/json',
                    'User-Agent': 'Invoice-Service/1.0',
                },
                timeout: 30000,
            }));
            this.logger.log(`Invoices retrieved successfully for customer: ${customer.user.username}`, {
                statusCode: response.status,
                statusText: response.statusText,
                requestParams: {
                    startDate: searchParams.startDate,
                    endDate: searchParams.endDate,
                    pageNum: searchParams.pageNum,
                    rowPerPage: searchParams.rowPerPage,
                },
            });
            return response.data;
        }
        catch (error) {
            if (error instanceof axios_2.AxiosError) {
                this.logger.error(`Failed to retrieve invoices for customer: ${customer.user.username}`, {
                    error: {
                        message: error.message,
                        code: error.code,
                        status: error.response?.status,
                        statusText: error.response?.statusText,
                        data: error.response?.data,
                    },
                    request: {
                        method: error.config?.method?.toUpperCase(),
                        url: error.config?.url,
                        params: searchParams,
                    },
                });
                return this.handleSinvoiceError(error);
            }
            this.logger.error(`Unexpected error retrieving invoices for customer: ${customer.user.username}`, {
                error: error instanceof Error ? error.stack : error,
            });
            throw new common_1.InternalServerErrorException({
                statusCode: 500,
                message: this.errorMessages.DEFAULT,
                error: 'Internal Server Error',
            });
        }
    }
};
exports.InvoiceService = InvoiceService;
exports.InvoiceService = InvoiceService = InvoiceService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [axios_1.HttpService,
        config_1.ConfigService,
        customer_service_1.CustomerService])
], InvoiceService);
//# sourceMappingURL=invoice.service.js.map