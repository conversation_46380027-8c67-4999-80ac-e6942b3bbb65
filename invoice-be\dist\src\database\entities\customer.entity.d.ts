import { User } from './user.entity';
export declare class Customer {
    id: string;
    userId: string;
    user: User;
    sinvoiceUsername: string;
    sinvoicePasswordEncrypted: string;
    sinvoicePasswordIv: string;
    sinvoicePasswordAuthTag: string | null;
    apiToken: string;
    sinvoiceAccessToken: string | null;
    sinvoiceRefreshToken: string | null;
    sinvoiceTokenExpiresAt: Date | null;
    createdAt: Date;
    updatedAt: Date;
    generateApiToken(): void;
    private generateSecureToken;
    regenerateApiToken(): void;
    static encryptSinvoicePassword(password: string): {
        encrypted: string;
        iv: string;
        authTag: string;
    };
    static decryptSinvoicePassword(encrypted: string, iv: string, authTag?: string | null): string;
    getSinvoicePassword(): string;
    setSinvoicePassword(password: string): void;
    migrateLegacyPassword(): void;
    updateSinvoiceTokens(accessToken: string, refreshToken: string, expiresIn: number): void;
    isSinvoiceTokenExpired(): boolean;
    clearSinvoiceTokens(): void;
    toJSON(): Omit<this, "toJSON" | "sinvoicePasswordEncrypted" | "sinvoicePasswordIv" | "sinvoiceAccessToken" | "sinvoiceRefreshToken" | "generateApiToken" | "regenerateApiToken" | "getSinvoicePassword" | "setSinvoicePassword" | "migrateLegacyPassword" | "updateSinvoiceTokens" | "isSinvoiceTokenExpired" | "clearSinvoiceTokens">;
}
