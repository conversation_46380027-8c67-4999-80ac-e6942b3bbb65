import { CustomerService } from '../services/customer.service';
import { CreateCustomerDto, CustomerResponseDto, UpdateCustomerDto, ApiTokenResponseDto, ResetApiTokenDto, SinvoiceLoginDto, SinvoiceLoginResponseDto } from '../dto';
import { User } from '../../../database/entities/user.entity';
export declare class CustomerController {
    private readonly customerService;
    constructor(customerService: CustomerService);
    createCustomer(createCustomerDto: CreateCustomerDto): Promise<CustomerResponseDto>;
    getAllCustomers(): Promise<CustomerResponseDto[]>;
    getCurrentCustomer(user: User): Promise<CustomerResponseDto>;
    getCustomerById(id: string): Promise<CustomerResponseDto>;
    updateCurrentCustomer(user: User, updateCustomerDto: UpdateCustomerDto): Promise<CustomerResponseDto>;
    updateCustomer(id: string, updateCustomerDto: UpdateCustomerDto): Promise<CustomerResponseDto>;
    resetApiToken(id: string, resetApiTokenDto: ResetApiTokenDto): Promise<ApiTokenResponseDto>;
    sinvoiceLogin(sinvoiceLoginDto: SinvoiceLoginDto): Promise<SinvoiceLoginResponseDto>;
    deleteCustomer(id: string): Promise<void>;
}
