"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const core_1 = require("@nestjs/core");
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const swagger_1 = require("@nestjs/swagger");
const helmet_1 = require("helmet");
const compression = require("compression");
const app_module_1 = require("./app.module");
const http_exception_filter_1 = require("./common/filters/http-exception.filter");
async function bootstrap() {
    const logger = new common_1.Logger('Bootstrap');
    const app = await core_1.NestFactory.create(app_module_1.AppModule);
    const configService = app.get(config_1.ConfigService);
    app.use((0, helmet_1.default)());
    app.use(compression());
    app.enableCors({
        origin: configService.get('app.corsOrigin'),
        credentials: true,
    });
    app.useGlobalPipes(new common_1.ValidationPipe({
        whitelist: true,
        forbidNonWhitelisted: true,
        transform: true,
        transformOptions: {
            enableImplicitConversion: true,
        },
    }));
    app.useGlobalFilters(new http_exception_filter_1.AllExceptionsFilter(), new http_exception_filter_1.HttpExceptionFilter());
    const apiPrefix = configService.get('app.apiPrefix');
    app.setGlobalPrefix(apiPrefix);
    if (configService.get('app.nodeEnv') !== 'production') {
        const config = new swagger_1.DocumentBuilder()
            .setTitle('Invoice Service API')
            .setDescription(`
Invoice Service Backend API Documentation

## Authentication
The API supports two authentication methods:
1. Bearer Token - For internal system authentication
2. API Token - For external service integration (Sinvoice)

### Bearer Token Authentication
Used for most endpoints. Obtain a token via the /auth/login endpoint.
Add the token to requests using the Authorization header:
\`\`\`
Authorization: Bearer <token>
\`\`\`

### API Token Authentication
Used specifically for Sinvoice integration endpoints.
Add your API token to requests using the X-API-Token header:
\`\`\`
X-API-Token: <your-api-token>
\`\`\`

## Rate Limiting
- Most endpoints are limited to 100 requests per minute
- Authentication endpoints are limited to 5 attempts per minute
- Sinvoice integration endpoints are limited to 5 requests per minute

## Error Handling
The API uses standard HTTP status codes and returns error responses in the following format:
\`\`\`json
{
  "statusCode": 400,
  "message": "Error description",
  "error": "Error type"
}
\`\`\`
      `)
            .setVersion('1.0')
            .addBearerAuth()
            .addApiKey({ type: 'apiKey', name: 'X-API-Token', in: 'header' }, 'X-API-Token')
            .addTag('Authentication', 'User authentication and session management')
            .addTag('Customers', 'Customer management and Sinvoice integration')
            .addTag('Invoices', 'Invoice creation and management through Sinvoice')
            .addTag('Health', 'System health monitoring')
            .build();
        const document = swagger_1.SwaggerModule.createDocument(app, config);
        swagger_1.SwaggerModule.setup(`${apiPrefix}/docs`, app, document);
    }
    const port = configService.get('app.port');
    await app.listen(port);
    logger.log(`🚀 Application is running on: http://localhost:${port}/${apiPrefix}`);
    if (configService.get('app.nodeEnv') !== 'production') {
        logger.log(`📚 Swagger documentation: http://localhost:${port}/${apiPrefix}/docs`);
    }
}
bootstrap().catch((error) => {
    common_1.Logger.error('❌ Error starting server', error, 'Bootstrap');
    process.exit(1);
});
//# sourceMappingURL=main.js.map