{"name": "date-fns", "version": "4.1.0", "contributors": ["<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>"], "license": "MIT", "description": "Modern JavaScript date utility library", "repository": "https://github.com/date-fns/date-fns", "funding": {"type": "github", "url": "https://github.com/sponsors/kossnocorp"}, "sideEffects": false, "browser": "./index", "type": "module", "main": "index.cjs", "module": "index.js", "jsdelivr": "./cdn.min.js", "exports": {"./package.json": "./package.json", ".": {"require": {"types": "./index.d.cts", "default": "./index.cjs"}, "import": {"types": "./index.d.ts", "default": "./index.js"}}, "./constants": {"require": {"types": "./constants.d.cts", "default": "./constants.cjs"}, "import": {"types": "./constants.d.ts", "default": "./constants.js"}}, "./locale": {"require": {"types": "./locale.d.cts", "default": "./locale.cjs"}, "import": {"types": "./locale.d.ts", "default": "./locale.js"}}, "./fp": {"require": {"types": "./fp.d.cts", "default": "./fp.cjs"}, "import": {"types": "./fp.d.ts", "default": "./fp.js"}}, "./add": {"require": {"types": "./add.d.cts", "default": "./add.cjs"}, "import": {"types": "./add.d.ts", "default": "./add.js"}}, "./addBusinessDays": {"require": {"types": "./addBusinessDays.d.cts", "default": "./addBusinessDays.cjs"}, "import": {"types": "./addBusinessDays.d.ts", "default": "./addBusinessDays.js"}}, "./addDays": {"require": {"types": "./addDays.d.cts", "default": "./addDays.cjs"}, "import": {"types": "./addDays.d.ts", "default": "./addDays.js"}}, "./addHours": {"require": {"types": "./addHours.d.cts", "default": "./addHours.cjs"}, "import": {"types": "./addHours.d.ts", "default": "./addHours.js"}}, "./addISOWeekYears": {"require": {"types": "./addISOWeekYears.d.cts", "default": "./addISOWeekYears.cjs"}, "import": {"types": "./addISOWeekYears.d.ts", "default": "./addISOWeekYears.js"}}, "./addMilliseconds": {"require": {"types": "./addMilliseconds.d.cts", "default": "./addMilliseconds.cjs"}, "import": {"types": "./addMilliseconds.d.ts", "default": "./addMilliseconds.js"}}, "./addMinutes": {"require": {"types": "./addMinutes.d.cts", "default": "./addMinutes.cjs"}, "import": {"types": "./addMinutes.d.ts", "default": "./addMinutes.js"}}, "./addMonths": {"require": {"types": "./addMonths.d.cts", "default": "./addMonths.cjs"}, "import": {"types": "./addMonths.d.ts", "default": "./addMonths.js"}}, "./addQuarters": {"require": {"types": "./addQuarters.d.cts", "default": "./addQuarters.cjs"}, "import": {"types": "./addQuarters.d.ts", "default": "./addQuarters.js"}}, "./addSeconds": {"require": {"types": "./addSeconds.d.cts", "default": "./addSeconds.cjs"}, "import": {"types": "./addSeconds.d.ts", "default": "./addSeconds.js"}}, "./addWeeks": {"require": {"types": "./addWeeks.d.cts", "default": "./addWeeks.cjs"}, "import": {"types": "./addWeeks.d.ts", "default": "./addWeeks.js"}}, "./addYears": {"require": {"types": "./addYears.d.cts", "default": "./addYears.cjs"}, "import": {"types": "./addYears.d.ts", "default": "./addYears.js"}}, "./areIntervalsOverlapping": {"require": {"types": "./areIntervalsOverlapping.d.cts", "default": "./areIntervalsOverlapping.cjs"}, "import": {"types": "./areIntervalsOverlapping.d.ts", "default": "./areIntervalsOverlapping.js"}}, "./clamp": {"require": {"types": "./clamp.d.cts", "default": "./clamp.cjs"}, "import": {"types": "./clamp.d.ts", "default": "./clamp.js"}}, "./closestIndexTo": {"require": {"types": "./closestIndexTo.d.cts", "default": "./closestIndexTo.cjs"}, "import": {"types": "./closestIndexTo.d.ts", "default": "./closestIndexTo.js"}}, "./closestTo": {"require": {"types": "./closestTo.d.cts", "default": "./closestTo.cjs"}, "import": {"types": "./closestTo.d.ts", "default": "./closestTo.js"}}, "./compareAsc": {"require": {"types": "./compareAsc.d.cts", "default": "./compareAsc.cjs"}, "import": {"types": "./compareAsc.d.ts", "default": "./compareAsc.js"}}, "./compareDesc": {"require": {"types": "./compareDesc.d.cts", "default": "./compareDesc.cjs"}, "import": {"types": "./compareDesc.d.ts", "default": "./compareDesc.js"}}, "./constructFrom": {"require": {"types": "./constructFrom.d.cts", "default": "./constructFrom.cjs"}, "import": {"types": "./constructFrom.d.ts", "default": "./constructFrom.js"}}, "./constructNow": {"require": {"types": "./constructNow.d.cts", "default": "./constructNow.cjs"}, "import": {"types": "./constructNow.d.ts", "default": "./constructNow.js"}}, "./daysToWeeks": {"require": {"types": "./daysToWeeks.d.cts", "default": "./daysToWeeks.cjs"}, "import": {"types": "./daysToWeeks.d.ts", "default": "./daysToWeeks.js"}}, "./differenceInBusinessDays": {"require": {"types": "./differenceInBusinessDays.d.cts", "default": "./differenceInBusinessDays.cjs"}, "import": {"types": "./differenceInBusinessDays.d.ts", "default": "./differenceInBusinessDays.js"}}, "./differenceInCalendarDays": {"require": {"types": "./differenceInCalendarDays.d.cts", "default": "./differenceInCalendarDays.cjs"}, "import": {"types": "./differenceInCalendarDays.d.ts", "default": "./differenceInCalendarDays.js"}}, "./differenceInCalendarISOWeekYears": {"require": {"types": "./differenceInCalendarISOWeekYears.d.cts", "default": "./differenceInCalendarISOWeekYears.cjs"}, "import": {"types": "./differenceInCalendarISOWeekYears.d.ts", "default": "./differenceInCalendarISOWeekYears.js"}}, "./differenceInCalendarISOWeeks": {"require": {"types": "./differenceInCalendarISOWeeks.d.cts", "default": "./differenceInCalendarISOWeeks.cjs"}, "import": {"types": "./differenceInCalendarISOWeeks.d.ts", "default": "./differenceInCalendarISOWeeks.js"}}, "./differenceInCalendarMonths": {"require": {"types": "./differenceInCalendarMonths.d.cts", "default": "./differenceInCalendarMonths.cjs"}, "import": {"types": "./differenceInCalendarMonths.d.ts", "default": "./differenceInCalendarMonths.js"}}, "./differenceInCalendarQuarters": {"require": {"types": "./differenceInCalendarQuarters.d.cts", "default": "./differenceInCalendarQuarters.cjs"}, "import": {"types": "./differenceInCalendarQuarters.d.ts", "default": "./differenceInCalendarQuarters.js"}}, "./differenceInCalendarWeeks": {"require": {"types": "./differenceInCalendarWeeks.d.cts", "default": "./differenceInCalendarWeeks.cjs"}, "import": {"types": "./differenceInCalendarWeeks.d.ts", "default": "./differenceInCalendarWeeks.js"}}, "./differenceInCalendarYears": {"require": {"types": "./differenceInCalendarYears.d.cts", "default": "./differenceInCalendarYears.cjs"}, "import": {"types": "./differenceInCalendarYears.d.ts", "default": "./differenceInCalendarYears.js"}}, "./differenceInDays": {"require": {"types": "./differenceInDays.d.cts", "default": "./differenceInDays.cjs"}, "import": {"types": "./differenceInDays.d.ts", "default": "./differenceInDays.js"}}, "./differenceInHours": {"require": {"types": "./differenceInHours.d.cts", "default": "./differenceInHours.cjs"}, "import": {"types": "./differenceInHours.d.ts", "default": "./differenceInHours.js"}}, "./differenceInISOWeekYears": {"require": {"types": "./differenceInISOWeekYears.d.cts", "default": "./differenceInISOWeekYears.cjs"}, "import": {"types": "./differenceInISOWeekYears.d.ts", "default": "./differenceInISOWeekYears.js"}}, "./differenceInMilliseconds": {"require": {"types": "./differenceInMilliseconds.d.cts", "default": "./differenceInMilliseconds.cjs"}, "import": {"types": "./differenceInMilliseconds.d.ts", "default": "./differenceInMilliseconds.js"}}, "./differenceInMinutes": {"require": {"types": "./differenceInMinutes.d.cts", "default": "./differenceInMinutes.cjs"}, "import": {"types": "./differenceInMinutes.d.ts", "default": "./differenceInMinutes.js"}}, "./differenceInMonths": {"require": {"types": "./differenceInMonths.d.cts", "default": "./differenceInMonths.cjs"}, "import": {"types": "./differenceInMonths.d.ts", "default": "./differenceInMonths.js"}}, "./differenceInQuarters": {"require": {"types": "./differenceInQuarters.d.cts", "default": "./differenceInQuarters.cjs"}, "import": {"types": "./differenceInQuarters.d.ts", "default": "./differenceInQuarters.js"}}, "./differenceInSeconds": {"require": {"types": "./differenceInSeconds.d.cts", "default": "./differenceInSeconds.cjs"}, "import": {"types": "./differenceInSeconds.d.ts", "default": "./differenceInSeconds.js"}}, "./differenceInWeeks": {"require": {"types": "./differenceInWeeks.d.cts", "default": "./differenceInWeeks.cjs"}, "import": {"types": "./differenceInWeeks.d.ts", "default": "./differenceInWeeks.js"}}, "./differenceInYears": {"require": {"types": "./differenceInYears.d.cts", "default": "./differenceInYears.cjs"}, "import": {"types": "./differenceInYears.d.ts", "default": "./differenceInYears.js"}}, "./eachDayOfInterval": {"require": {"types": "./eachDayOfInterval.d.cts", "default": "./eachDayOfInterval.cjs"}, "import": {"types": "./eachDayOfInterval.d.ts", "default": "./eachDayOfInterval.js"}}, "./eachHourOfInterval": {"require": {"types": "./eachHourOfInterval.d.cts", "default": "./eachHourOfInterval.cjs"}, "import": {"types": "./eachHourOfInterval.d.ts", "default": "./eachHourOfInterval.js"}}, "./eachMinuteOfInterval": {"require": {"types": "./eachMinuteOfInterval.d.cts", "default": "./eachMinuteOfInterval.cjs"}, "import": {"types": "./eachMinuteOfInterval.d.ts", "default": "./eachMinuteOfInterval.js"}}, "./eachMonthOfInterval": {"require": {"types": "./eachMonthOfInterval.d.cts", "default": "./eachMonthOfInterval.cjs"}, "import": {"types": "./eachMonthOfInterval.d.ts", "default": "./eachMonthOfInterval.js"}}, "./eachQuarterOfInterval": {"require": {"types": "./eachQuarterOfInterval.d.cts", "default": "./eachQuarterOfInterval.cjs"}, "import": {"types": "./eachQuarterOfInterval.d.ts", "default": "./eachQuarterOfInterval.js"}}, "./eachWeekOfInterval": {"require": {"types": "./eachWeekOfInterval.d.cts", "default": "./eachWeekOfInterval.cjs"}, "import": {"types": "./eachWeekOfInterval.d.ts", "default": "./eachWeekOfInterval.js"}}, "./eachWeekendOfInterval": {"require": {"types": "./eachWeekendOfInterval.d.cts", "default": "./eachWeekendOfInterval.cjs"}, "import": {"types": "./eachWeekendOfInterval.d.ts", "default": "./eachWeekendOfInterval.js"}}, "./eachWeekendOfMonth": {"require": {"types": "./eachWeekendOfMonth.d.cts", "default": "./eachWeekendOfMonth.cjs"}, "import": {"types": "./eachWeekendOfMonth.d.ts", "default": "./eachWeekendOfMonth.js"}}, "./eachWeekendOfYear": {"require": {"types": "./eachWeekendOfYear.d.cts", "default": "./eachWeekendOfYear.cjs"}, "import": {"types": "./eachWeekendOfYear.d.ts", "default": "./eachWeekendOfYear.js"}}, "./eachYearOfInterval": {"require": {"types": "./eachYearOfInterval.d.cts", "default": "./eachYearOfInterval.cjs"}, "import": {"types": "./eachYearOfInterval.d.ts", "default": "./eachYearOfInterval.js"}}, "./endOfDay": {"require": {"types": "./endOfDay.d.cts", "default": "./endOfDay.cjs"}, "import": {"types": "./endOfDay.d.ts", "default": "./endOfDay.js"}}, "./endOfDecade": {"require": {"types": "./endOfDecade.d.cts", "default": "./endOfDecade.cjs"}, "import": {"types": "./endOfDecade.d.ts", "default": "./endOfDecade.js"}}, "./endOfHour": {"require": {"types": "./endOfHour.d.cts", "default": "./endOfHour.cjs"}, "import": {"types": "./endOfHour.d.ts", "default": "./endOfHour.js"}}, "./endOfISOWeek": {"require": {"types": "./endOfISOWeek.d.cts", "default": "./endOfISOWeek.cjs"}, "import": {"types": "./endOfISOWeek.d.ts", "default": "./endOfISOWeek.js"}}, "./endOfISOWeekYear": {"require": {"types": "./endOfISOWeekYear.d.cts", "default": "./endOfISOWeekYear.cjs"}, "import": {"types": "./endOfISOWeekYear.d.ts", "default": "./endOfISOWeekYear.js"}}, "./endOfMinute": {"require": {"types": "./endOfMinute.d.cts", "default": "./endOfMinute.cjs"}, "import": {"types": "./endOfMinute.d.ts", "default": "./endOfMinute.js"}}, "./endOfMonth": {"require": {"types": "./endOfMonth.d.cts", "default": "./endOfMonth.cjs"}, "import": {"types": "./endOfMonth.d.ts", "default": "./endOfMonth.js"}}, "./endOfQuarter": {"require": {"types": "./endOfQuarter.d.cts", "default": "./endOfQuarter.cjs"}, "import": {"types": "./endOfQuarter.d.ts", "default": "./endOfQuarter.js"}}, "./endOfSecond": {"require": {"types": "./endOfSecond.d.cts", "default": "./endOfSecond.cjs"}, "import": {"types": "./endOfSecond.d.ts", "default": "./endOfSecond.js"}}, "./endOfToday": {"require": {"types": "./endOfToday.d.cts", "default": "./endOfToday.cjs"}, "import": {"types": "./endOfToday.d.ts", "default": "./endOfToday.js"}}, "./endOfTomorrow": {"require": {"types": "./endOfTomorrow.d.cts", "default": "./endOfTomorrow.cjs"}, "import": {"types": "./endOfTomorrow.d.ts", "default": "./endOfTomorrow.js"}}, "./endOfWeek": {"require": {"types": "./endOfWeek.d.cts", "default": "./endOfWeek.cjs"}, "import": {"types": "./endOfWeek.d.ts", "default": "./endOfWeek.js"}}, "./endOfYear": {"require": {"types": "./endOfYear.d.cts", "default": "./endOfYear.cjs"}, "import": {"types": "./endOfYear.d.ts", "default": "./endOfYear.js"}}, "./endOfYesterday": {"require": {"types": "./endOfYesterday.d.cts", "default": "./endOfYesterday.cjs"}, "import": {"types": "./endOfYesterday.d.ts", "default": "./endOfYesterday.js"}}, "./format": {"require": {"types": "./format.d.cts", "default": "./format.cjs"}, "import": {"types": "./format.d.ts", "default": "./format.js"}}, "./formatDistance": {"require": {"types": "./formatDistance.d.cts", "default": "./formatDistance.cjs"}, "import": {"types": "./formatDistance.d.ts", "default": "./formatDistance.js"}}, "./formatDistanceStrict": {"require": {"types": "./formatDistanceStrict.d.cts", "default": "./formatDistanceStrict.cjs"}, "import": {"types": "./formatDistanceStrict.d.ts", "default": "./formatDistanceStrict.js"}}, "./formatDistanceToNow": {"require": {"types": "./formatDistanceToNow.d.cts", "default": "./formatDistanceToNow.cjs"}, "import": {"types": "./formatDistanceToNow.d.ts", "default": "./formatDistanceToNow.js"}}, "./formatDistanceToNowStrict": {"require": {"types": "./formatDistanceToNowStrict.d.cts", "default": "./formatDistanceToNowStrict.cjs"}, "import": {"types": "./formatDistanceToNowStrict.d.ts", "default": "./formatDistanceToNowStrict.js"}}, "./formatDuration": {"require": {"types": "./formatDuration.d.cts", "default": "./formatDuration.cjs"}, "import": {"types": "./formatDuration.d.ts", "default": "./formatDuration.js"}}, "./formatISO": {"require": {"types": "./formatISO.d.cts", "default": "./formatISO.cjs"}, "import": {"types": "./formatISO.d.ts", "default": "./formatISO.js"}}, "./formatISO9075": {"require": {"types": "./formatISO9075.d.cts", "default": "./formatISO9075.cjs"}, "import": {"types": "./formatISO9075.d.ts", "default": "./formatISO9075.js"}}, "./formatISODuration": {"require": {"types": "./formatISODuration.d.cts", "default": "./formatISODuration.cjs"}, "import": {"types": "./formatISODuration.d.ts", "default": "./formatISODuration.js"}}, "./formatRFC3339": {"require": {"types": "./formatRFC3339.d.cts", "default": "./formatRFC3339.cjs"}, "import": {"types": "./formatRFC3339.d.ts", "default": "./formatRFC3339.js"}}, "./formatRFC7231": {"require": {"types": "./formatRFC7231.d.cts", "default": "./formatRFC7231.cjs"}, "import": {"types": "./formatRFC7231.d.ts", "default": "./formatRFC7231.js"}}, "./formatRelative": {"require": {"types": "./formatRelative.d.cts", "default": "./formatRelative.cjs"}, "import": {"types": "./formatRelative.d.ts", "default": "./formatRelative.js"}}, "./fromUnixTime": {"require": {"types": "./fromUnixTime.d.cts", "default": "./fromUnixTime.cjs"}, "import": {"types": "./fromUnixTime.d.ts", "default": "./fromUnixTime.js"}}, "./getDate": {"require": {"types": "./getDate.d.cts", "default": "./getDate.cjs"}, "import": {"types": "./getDate.d.ts", "default": "./getDate.js"}}, "./getDay": {"require": {"types": "./getDay.d.cts", "default": "./getDay.cjs"}, "import": {"types": "./getDay.d.ts", "default": "./getDay.js"}}, "./getDayOfYear": {"require": {"types": "./getDayOfYear.d.cts", "default": "./getDayOfYear.cjs"}, "import": {"types": "./getDayOfYear.d.ts", "default": "./getDayOfYear.js"}}, "./getDaysInMonth": {"require": {"types": "./getDaysInMonth.d.cts", "default": "./getDaysInMonth.cjs"}, "import": {"types": "./getDaysInMonth.d.ts", "default": "./getDaysInMonth.js"}}, "./getDaysInYear": {"require": {"types": "./getDaysInYear.d.cts", "default": "./getDaysInYear.cjs"}, "import": {"types": "./getDaysInYear.d.ts", "default": "./getDaysInYear.js"}}, "./getDecade": {"require": {"types": "./getDecade.d.cts", "default": "./getDecade.cjs"}, "import": {"types": "./getDecade.d.ts", "default": "./getDecade.js"}}, "./getDefaultOptions": {"require": {"types": "./getDefaultOptions.d.cts", "default": "./getDefaultOptions.cjs"}, "import": {"types": "./getDefaultOptions.d.ts", "default": "./getDefaultOptions.js"}}, "./getHours": {"require": {"types": "./getHours.d.cts", "default": "./getHours.cjs"}, "import": {"types": "./getHours.d.ts", "default": "./getHours.js"}}, "./getISODay": {"require": {"types": "./getISODay.d.cts", "default": "./getISODay.cjs"}, "import": {"types": "./getISODay.d.ts", "default": "./getISODay.js"}}, "./getISOWeek": {"require": {"types": "./getISOWeek.d.cts", "default": "./getISOWeek.cjs"}, "import": {"types": "./getISOWeek.d.ts", "default": "./getISOWeek.js"}}, "./getISOWeekYear": {"require": {"types": "./getISOWeekYear.d.cts", "default": "./getISOWeekYear.cjs"}, "import": {"types": "./getISOWeekYear.d.ts", "default": "./getISOWeekYear.js"}}, "./getISOWeeksInYear": {"require": {"types": "./getISOWeeksInYear.d.cts", "default": "./getISOWeeksInYear.cjs"}, "import": {"types": "./getISOWeeksInYear.d.ts", "default": "./getISOWeeksInYear.js"}}, "./getMilliseconds": {"require": {"types": "./getMilliseconds.d.cts", "default": "./getMilliseconds.cjs"}, "import": {"types": "./getMilliseconds.d.ts", "default": "./getMilliseconds.js"}}, "./getMinutes": {"require": {"types": "./getMinutes.d.cts", "default": "./getMinutes.cjs"}, "import": {"types": "./getMinutes.d.ts", "default": "./getMinutes.js"}}, "./getMonth": {"require": {"types": "./getMonth.d.cts", "default": "./getMonth.cjs"}, "import": {"types": "./getMonth.d.ts", "default": "./getMonth.js"}}, "./getOverlappingDaysInIntervals": {"require": {"types": "./getOverlappingDaysInIntervals.d.cts", "default": "./getOverlappingDaysInIntervals.cjs"}, "import": {"types": "./getOverlappingDaysInIntervals.d.ts", "default": "./getOverlappingDaysInIntervals.js"}}, "./getQuarter": {"require": {"types": "./getQuarter.d.cts", "default": "./getQuarter.cjs"}, "import": {"types": "./getQuarter.d.ts", "default": "./getQuarter.js"}}, "./getSeconds": {"require": {"types": "./getSeconds.d.cts", "default": "./getSeconds.cjs"}, "import": {"types": "./getSeconds.d.ts", "default": "./getSeconds.js"}}, "./getTime": {"require": {"types": "./getTime.d.cts", "default": "./getTime.cjs"}, "import": {"types": "./getTime.d.ts", "default": "./getTime.js"}}, "./getUnixTime": {"require": {"types": "./getUnixTime.d.cts", "default": "./getUnixTime.cjs"}, "import": {"types": "./getUnixTime.d.ts", "default": "./getUnixTime.js"}}, "./getWeek": {"require": {"types": "./getWeek.d.cts", "default": "./getWeek.cjs"}, "import": {"types": "./getWeek.d.ts", "default": "./getWeek.js"}}, "./getWeekOfMonth": {"require": {"types": "./getWeekOfMonth.d.cts", "default": "./getWeekOfMonth.cjs"}, "import": {"types": "./getWeekOfMonth.d.ts", "default": "./getWeekOfMonth.js"}}, "./getWeekYear": {"require": {"types": "./getWeekYear.d.cts", "default": "./getWeekYear.cjs"}, "import": {"types": "./getWeekYear.d.ts", "default": "./getWeekYear.js"}}, "./getWeeksInMonth": {"require": {"types": "./getWeeksInMonth.d.cts", "default": "./getWeeksInMonth.cjs"}, "import": {"types": "./getWeeksInMonth.d.ts", "default": "./getWeeksInMonth.js"}}, "./getYear": {"require": {"types": "./getYear.d.cts", "default": "./getYear.cjs"}, "import": {"types": "./getYear.d.ts", "default": "./getYear.js"}}, "./hoursToMilliseconds": {"require": {"types": "./hoursToMilliseconds.d.cts", "default": "./hoursToMilliseconds.cjs"}, "import": {"types": "./hoursToMilliseconds.d.ts", "default": "./hoursToMilliseconds.js"}}, "./hoursToMinutes": {"require": {"types": "./hoursToMinutes.d.cts", "default": "./hoursToMinutes.cjs"}, "import": {"types": "./hoursToMinutes.d.ts", "default": "./hoursToMinutes.js"}}, "./hoursToSeconds": {"require": {"types": "./hoursToSeconds.d.cts", "default": "./hoursToSeconds.cjs"}, "import": {"types": "./hoursToSeconds.d.ts", "default": "./hoursToSeconds.js"}}, "./interval": {"require": {"types": "./interval.d.cts", "default": "./interval.cjs"}, "import": {"types": "./interval.d.ts", "default": "./interval.js"}}, "./intervalToDuration": {"require": {"types": "./intervalToDuration.d.cts", "default": "./intervalToDuration.cjs"}, "import": {"types": "./intervalToDuration.d.ts", "default": "./intervalToDuration.js"}}, "./intlFormat": {"require": {"types": "./intlFormat.d.cts", "default": "./intlFormat.cjs"}, "import": {"types": "./intlFormat.d.ts", "default": "./intlFormat.js"}}, "./intlFormatDistance": {"require": {"types": "./intlFormatDistance.d.cts", "default": "./intlFormatDistance.cjs"}, "import": {"types": "./intlFormatDistance.d.ts", "default": "./intlFormatDistance.js"}}, "./isAfter": {"require": {"types": "./isAfter.d.cts", "default": "./isAfter.cjs"}, "import": {"types": "./isAfter.d.ts", "default": "./isAfter.js"}}, "./isBefore": {"require": {"types": "./isBefore.d.cts", "default": "./isBefore.cjs"}, "import": {"types": "./isBefore.d.ts", "default": "./isBefore.js"}}, "./isDate": {"require": {"types": "./isDate.d.cts", "default": "./isDate.cjs"}, "import": {"types": "./isDate.d.ts", "default": "./isDate.js"}}, "./isEqual": {"require": {"types": "./isEqual.d.cts", "default": "./isEqual.cjs"}, "import": {"types": "./isEqual.d.ts", "default": "./isEqual.js"}}, "./isExists": {"require": {"types": "./isExists.d.cts", "default": "./isExists.cjs"}, "import": {"types": "./isExists.d.ts", "default": "./isExists.js"}}, "./isFirstDayOfMonth": {"require": {"types": "./isFirstDayOfMonth.d.cts", "default": "./isFirstDayOfMonth.cjs"}, "import": {"types": "./isFirstDayOfMonth.d.ts", "default": "./isFirstDayOfMonth.js"}}, "./isFriday": {"require": {"types": "./isFriday.d.cts", "default": "./isFriday.cjs"}, "import": {"types": "./isFriday.d.ts", "default": "./isFriday.js"}}, "./isFuture": {"require": {"types": "./isFuture.d.cts", "default": "./isFuture.cjs"}, "import": {"types": "./isFuture.d.ts", "default": "./isFuture.js"}}, "./isLastDayOfMonth": {"require": {"types": "./isLastDayOfMonth.d.cts", "default": "./isLastDayOfMonth.cjs"}, "import": {"types": "./isLastDayOfMonth.d.ts", "default": "./isLastDayOfMonth.js"}}, "./isLeapYear": {"require": {"types": "./isLeapYear.d.cts", "default": "./isLeapYear.cjs"}, "import": {"types": "./isLeapYear.d.ts", "default": "./isLeapYear.js"}}, "./isMatch": {"require": {"types": "./isMatch.d.cts", "default": "./isMatch.cjs"}, "import": {"types": "./isMatch.d.ts", "default": "./isMatch.js"}}, "./isMonday": {"require": {"types": "./isMonday.d.cts", "default": "./isMonday.cjs"}, "import": {"types": "./isMonday.d.ts", "default": "./isMonday.js"}}, "./isPast": {"require": {"types": "./isPast.d.cts", "default": "./isPast.cjs"}, "import": {"types": "./isPast.d.ts", "default": "./isPast.js"}}, "./isSameDay": {"require": {"types": "./isSameDay.d.cts", "default": "./isSameDay.cjs"}, "import": {"types": "./isSameDay.d.ts", "default": "./isSameDay.js"}}, "./isSameHour": {"require": {"types": "./isSameHour.d.cts", "default": "./isSameHour.cjs"}, "import": {"types": "./isSameHour.d.ts", "default": "./isSameHour.js"}}, "./isSameISOWeek": {"require": {"types": "./isSameISOWeek.d.cts", "default": "./isSameISOWeek.cjs"}, "import": {"types": "./isSameISOWeek.d.ts", "default": "./isSameISOWeek.js"}}, "./isSameISOWeekYear": {"require": {"types": "./isSameISOWeekYear.d.cts", "default": "./isSameISOWeekYear.cjs"}, "import": {"types": "./isSameISOWeekYear.d.ts", "default": "./isSameISOWeekYear.js"}}, "./isSameMinute": {"require": {"types": "./isSameMinute.d.cts", "default": "./isSameMinute.cjs"}, "import": {"types": "./isSameMinute.d.ts", "default": "./isSameMinute.js"}}, "./isSameMonth": {"require": {"types": "./isSameMonth.d.cts", "default": "./isSameMonth.cjs"}, "import": {"types": "./isSameMonth.d.ts", "default": "./isSameMonth.js"}}, "./isSameQuarter": {"require": {"types": "./isSameQuarter.d.cts", "default": "./isSameQuarter.cjs"}, "import": {"types": "./isSameQuarter.d.ts", "default": "./isSameQuarter.js"}}, "./isSameSecond": {"require": {"types": "./isSameSecond.d.cts", "default": "./isSameSecond.cjs"}, "import": {"types": "./isSameSecond.d.ts", "default": "./isSameSecond.js"}}, "./isSameWeek": {"require": {"types": "./isSameWeek.d.cts", "default": "./isSameWeek.cjs"}, "import": {"types": "./isSameWeek.d.ts", "default": "./isSameWeek.js"}}, "./isSameYear": {"require": {"types": "./isSameYear.d.cts", "default": "./isSameYear.cjs"}, "import": {"types": "./isSameYear.d.ts", "default": "./isSameYear.js"}}, "./isSaturday": {"require": {"types": "./isSaturday.d.cts", "default": "./isSaturday.cjs"}, "import": {"types": "./isSaturday.d.ts", "default": "./isSaturday.js"}}, "./isSunday": {"require": {"types": "./isSunday.d.cts", "default": "./isSunday.cjs"}, "import": {"types": "./isSunday.d.ts", "default": "./isSunday.js"}}, "./isThisHour": {"require": {"types": "./isThisHour.d.cts", "default": "./isThisHour.cjs"}, "import": {"types": "./isThisHour.d.ts", "default": "./isThisHour.js"}}, "./isThisISOWeek": {"require": {"types": "./isThisISOWeek.d.cts", "default": "./isThisISOWeek.cjs"}, "import": {"types": "./isThisISOWeek.d.ts", "default": "./isThisISOWeek.js"}}, "./isThisMinute": {"require": {"types": "./isThisMinute.d.cts", "default": "./isThisMinute.cjs"}, "import": {"types": "./isThisMinute.d.ts", "default": "./isThisMinute.js"}}, "./isThisMonth": {"require": {"types": "./isThisMonth.d.cts", "default": "./isThisMonth.cjs"}, "import": {"types": "./isThisMonth.d.ts", "default": "./isThisMonth.js"}}, "./isThisQuarter": {"require": {"types": "./isThisQuarter.d.cts", "default": "./isThisQuarter.cjs"}, "import": {"types": "./isThisQuarter.d.ts", "default": "./isThisQuarter.js"}}, "./isThisSecond": {"require": {"types": "./isThisSecond.d.cts", "default": "./isThisSecond.cjs"}, "import": {"types": "./isThisSecond.d.ts", "default": "./isThisSecond.js"}}, "./isThisWeek": {"require": {"types": "./isThisWeek.d.cts", "default": "./isThisWeek.cjs"}, "import": {"types": "./isThisWeek.d.ts", "default": "./isThisWeek.js"}}, "./isThisYear": {"require": {"types": "./isThisYear.d.cts", "default": "./isThisYear.cjs"}, "import": {"types": "./isThisYear.d.ts", "default": "./isThisYear.js"}}, "./isThursday": {"require": {"types": "./isThursday.d.cts", "default": "./isThursday.cjs"}, "import": {"types": "./isThursday.d.ts", "default": "./isThursday.js"}}, "./isToday": {"require": {"types": "./isToday.d.cts", "default": "./isToday.cjs"}, "import": {"types": "./isToday.d.ts", "default": "./isToday.js"}}, "./isTomorrow": {"require": {"types": "./isTomorrow.d.cts", "default": "./isTomorrow.cjs"}, "import": {"types": "./isTomorrow.d.ts", "default": "./isTomorrow.js"}}, "./isTuesday": {"require": {"types": "./isTuesday.d.cts", "default": "./isTuesday.cjs"}, "import": {"types": "./isTuesday.d.ts", "default": "./isTuesday.js"}}, "./isValid": {"require": {"types": "./isValid.d.cts", "default": "./isValid.cjs"}, "import": {"types": "./isValid.d.ts", "default": "./isValid.js"}}, "./isWednesday": {"require": {"types": "./isWednesday.d.cts", "default": "./isWednesday.cjs"}, "import": {"types": "./isWednesday.d.ts", "default": "./isWednesday.js"}}, "./isWeekend": {"require": {"types": "./isWeekend.d.cts", "default": "./isWeekend.cjs"}, "import": {"types": "./isWeekend.d.ts", "default": "./isWeekend.js"}}, "./isWithinInterval": {"require": {"types": "./isWithinInterval.d.cts", "default": "./isWithinInterval.cjs"}, "import": {"types": "./isWithinInterval.d.ts", "default": "./isWithinInterval.js"}}, "./isYesterday": {"require": {"types": "./isYesterday.d.cts", "default": "./isYesterday.cjs"}, "import": {"types": "./isYesterday.d.ts", "default": "./isYesterday.js"}}, "./lastDayOfDecade": {"require": {"types": "./lastDayOfDecade.d.cts", "default": "./lastDayOfDecade.cjs"}, "import": {"types": "./lastDayOfDecade.d.ts", "default": "./lastDayOfDecade.js"}}, "./lastDayOfISOWeek": {"require": {"types": "./lastDayOfISOWeek.d.cts", "default": "./lastDayOfISOWeek.cjs"}, "import": {"types": "./lastDayOfISOWeek.d.ts", "default": "./lastDayOfISOWeek.js"}}, "./lastDayOfISOWeekYear": {"require": {"types": "./lastDayOfISOWeekYear.d.cts", "default": "./lastDayOfISOWeekYear.cjs"}, "import": {"types": "./lastDayOfISOWeekYear.d.ts", "default": "./lastDayOfISOWeekYear.js"}}, "./lastDayOfMonth": {"require": {"types": "./lastDayOfMonth.d.cts", "default": "./lastDayOfMonth.cjs"}, "import": {"types": "./lastDayOfMonth.d.ts", "default": "./lastDayOfMonth.js"}}, "./lastDayOfQuarter": {"require": {"types": "./lastDayOfQuarter.d.cts", "default": "./lastDayOfQuarter.cjs"}, "import": {"types": "./lastDayOfQuarter.d.ts", "default": "./lastDayOfQuarter.js"}}, "./lastDayOfWeek": {"require": {"types": "./lastDayOfWeek.d.cts", "default": "./lastDayOfWeek.cjs"}, "import": {"types": "./lastDayOfWeek.d.ts", "default": "./lastDayOfWeek.js"}}, "./lastDayOfYear": {"require": {"types": "./lastDayOfYear.d.cts", "default": "./lastDayOfYear.cjs"}, "import": {"types": "./lastDayOfYear.d.ts", "default": "./lastDayOfYear.js"}}, "./lightFormat": {"require": {"types": "./lightFormat.d.cts", "default": "./lightFormat.cjs"}, "import": {"types": "./lightFormat.d.ts", "default": "./lightFormat.js"}}, "./max": {"require": {"types": "./max.d.cts", "default": "./max.cjs"}, "import": {"types": "./max.d.ts", "default": "./max.js"}}, "./milliseconds": {"require": {"types": "./milliseconds.d.cts", "default": "./milliseconds.cjs"}, "import": {"types": "./milliseconds.d.ts", "default": "./milliseconds.js"}}, "./millisecondsToHours": {"require": {"types": "./millisecondsToHours.d.cts", "default": "./millisecondsToHours.cjs"}, "import": {"types": "./millisecondsToHours.d.ts", "default": "./millisecondsToHours.js"}}, "./millisecondsToMinutes": {"require": {"types": "./millisecondsToMinutes.d.cts", "default": "./millisecondsToMinutes.cjs"}, "import": {"types": "./millisecondsToMinutes.d.ts", "default": "./millisecondsToMinutes.js"}}, "./millisecondsToSeconds": {"require": {"types": "./millisecondsToSeconds.d.cts", "default": "./millisecondsToSeconds.cjs"}, "import": {"types": "./millisecondsToSeconds.d.ts", "default": "./millisecondsToSeconds.js"}}, "./min": {"require": {"types": "./min.d.cts", "default": "./min.cjs"}, "import": {"types": "./min.d.ts", "default": "./min.js"}}, "./minutesToHours": {"require": {"types": "./minutesToHours.d.cts", "default": "./minutesToHours.cjs"}, "import": {"types": "./minutesToHours.d.ts", "default": "./minutesToHours.js"}}, "./minutesToMilliseconds": {"require": {"types": "./minutesToMilliseconds.d.cts", "default": "./minutesToMilliseconds.cjs"}, "import": {"types": "./minutesToMilliseconds.d.ts", "default": "./minutesToMilliseconds.js"}}, "./minutesToSeconds": {"require": {"types": "./minutesToSeconds.d.cts", "default": "./minutesToSeconds.cjs"}, "import": {"types": "./minutesToSeconds.d.ts", "default": "./minutesToSeconds.js"}}, "./monthsToQuarters": {"require": {"types": "./monthsToQuarters.d.cts", "default": "./monthsToQuarters.cjs"}, "import": {"types": "./monthsToQuarters.d.ts", "default": "./monthsToQuarters.js"}}, "./monthsToYears": {"require": {"types": "./monthsToYears.d.cts", "default": "./monthsToYears.cjs"}, "import": {"types": "./monthsToYears.d.ts", "default": "./monthsToYears.js"}}, "./nextDay": {"require": {"types": "./nextDay.d.cts", "default": "./nextDay.cjs"}, "import": {"types": "./nextDay.d.ts", "default": "./nextDay.js"}}, "./nextFriday": {"require": {"types": "./nextFriday.d.cts", "default": "./nextFriday.cjs"}, "import": {"types": "./nextFriday.d.ts", "default": "./nextFriday.js"}}, "./nextMonday": {"require": {"types": "./nextMonday.d.cts", "default": "./nextMonday.cjs"}, "import": {"types": "./nextMonday.d.ts", "default": "./nextMonday.js"}}, "./nextSaturday": {"require": {"types": "./nextSaturday.d.cts", "default": "./nextSaturday.cjs"}, "import": {"types": "./nextSaturday.d.ts", "default": "./nextSaturday.js"}}, "./nextSunday": {"require": {"types": "./nextSunday.d.cts", "default": "./nextSunday.cjs"}, "import": {"types": "./nextSunday.d.ts", "default": "./nextSunday.js"}}, "./nextThursday": {"require": {"types": "./nextThursday.d.cts", "default": "./nextThursday.cjs"}, "import": {"types": "./nextThursday.d.ts", "default": "./nextThursday.js"}}, "./nextTuesday": {"require": {"types": "./nextTuesday.d.cts", "default": "./nextTuesday.cjs"}, "import": {"types": "./nextTuesday.d.ts", "default": "./nextTuesday.js"}}, "./nextWednesday": {"require": {"types": "./nextWednesday.d.cts", "default": "./nextWednesday.cjs"}, "import": {"types": "./nextWednesday.d.ts", "default": "./nextWednesday.js"}}, "./parse": {"require": {"types": "./parse.d.cts", "default": "./parse.cjs"}, "import": {"types": "./parse.d.ts", "default": "./parse.js"}}, "./parseISO": {"require": {"types": "./parseISO.d.cts", "default": "./parseISO.cjs"}, "import": {"types": "./parseISO.d.ts", "default": "./parseISO.js"}}, "./parseJSON": {"require": {"types": "./parseJSON.d.cts", "default": "./parseJSON.cjs"}, "import": {"types": "./parseJSON.d.ts", "default": "./parseJSON.js"}}, "./previousDay": {"require": {"types": "./previousDay.d.cts", "default": "./previousDay.cjs"}, "import": {"types": "./previousDay.d.ts", "default": "./previousDay.js"}}, "./previousFriday": {"require": {"types": "./previousFriday.d.cts", "default": "./previousFriday.cjs"}, "import": {"types": "./previousFriday.d.ts", "default": "./previousFriday.js"}}, "./previousMonday": {"require": {"types": "./previousMonday.d.cts", "default": "./previousMonday.cjs"}, "import": {"types": "./previousMonday.d.ts", "default": "./previousMonday.js"}}, "./previousSaturday": {"require": {"types": "./previousSaturday.d.cts", "default": "./previousSaturday.cjs"}, "import": {"types": "./previousSaturday.d.ts", "default": "./previousSaturday.js"}}, "./previousSunday": {"require": {"types": "./previousSunday.d.cts", "default": "./previousSunday.cjs"}, "import": {"types": "./previousSunday.d.ts", "default": "./previousSunday.js"}}, "./previousThursday": {"require": {"types": "./previousThursday.d.cts", "default": "./previousThursday.cjs"}, "import": {"types": "./previousThursday.d.ts", "default": "./previousThursday.js"}}, "./previousTuesday": {"require": {"types": "./previousTuesday.d.cts", "default": "./previousTuesday.cjs"}, "import": {"types": "./previousTuesday.d.ts", "default": "./previousTuesday.js"}}, "./previousWednesday": {"require": {"types": "./previousWednesday.d.cts", "default": "./previousWednesday.cjs"}, "import": {"types": "./previousWednesday.d.ts", "default": "./previousWednesday.js"}}, "./quartersToMonths": {"require": {"types": "./quartersToMonths.d.cts", "default": "./quartersToMonths.cjs"}, "import": {"types": "./quartersToMonths.d.ts", "default": "./quartersToMonths.js"}}, "./quartersToYears": {"require": {"types": "./quartersToYears.d.cts", "default": "./quartersToYears.cjs"}, "import": {"types": "./quartersToYears.d.ts", "default": "./quartersToYears.js"}}, "./roundToNearestHours": {"require": {"types": "./roundToNearestHours.d.cts", "default": "./roundToNearestHours.cjs"}, "import": {"types": "./roundToNearestHours.d.ts", "default": "./roundToNearestHours.js"}}, "./roundToNearestMinutes": {"require": {"types": "./roundToNearestMinutes.d.cts", "default": "./roundToNearestMinutes.cjs"}, "import": {"types": "./roundToNearestMinutes.d.ts", "default": "./roundToNearestMinutes.js"}}, "./secondsToHours": {"require": {"types": "./secondsToHours.d.cts", "default": "./secondsToHours.cjs"}, "import": {"types": "./secondsToHours.d.ts", "default": "./secondsToHours.js"}}, "./secondsToMilliseconds": {"require": {"types": "./secondsToMilliseconds.d.cts", "default": "./secondsToMilliseconds.cjs"}, "import": {"types": "./secondsToMilliseconds.d.ts", "default": "./secondsToMilliseconds.js"}}, "./secondsToMinutes": {"require": {"types": "./secondsToMinutes.d.cts", "default": "./secondsToMinutes.cjs"}, "import": {"types": "./secondsToMinutes.d.ts", "default": "./secondsToMinutes.js"}}, "./set": {"require": {"types": "./set.d.cts", "default": "./set.cjs"}, "import": {"types": "./set.d.ts", "default": "./set.js"}}, "./setDate": {"require": {"types": "./setDate.d.cts", "default": "./setDate.cjs"}, "import": {"types": "./setDate.d.ts", "default": "./setDate.js"}}, "./setDay": {"require": {"types": "./setDay.d.cts", "default": "./setDay.cjs"}, "import": {"types": "./setDay.d.ts", "default": "./setDay.js"}}, "./setDayOfYear": {"require": {"types": "./setDayOfYear.d.cts", "default": "./setDayOfYear.cjs"}, "import": {"types": "./setDayOfYear.d.ts", "default": "./setDayOfYear.js"}}, "./setDefaultOptions": {"require": {"types": "./setDefaultOptions.d.cts", "default": "./setDefaultOptions.cjs"}, "import": {"types": "./setDefaultOptions.d.ts", "default": "./setDefaultOptions.js"}}, "./setHours": {"require": {"types": "./setHours.d.cts", "default": "./setHours.cjs"}, "import": {"types": "./setHours.d.ts", "default": "./setHours.js"}}, "./setISODay": {"require": {"types": "./setISODay.d.cts", "default": "./setISODay.cjs"}, "import": {"types": "./setISODay.d.ts", "default": "./setISODay.js"}}, "./setISOWeek": {"require": {"types": "./setISOWeek.d.cts", "default": "./setISOWeek.cjs"}, "import": {"types": "./setISOWeek.d.ts", "default": "./setISOWeek.js"}}, "./setISOWeekYear": {"require": {"types": "./setISOWeekYear.d.cts", "default": "./setISOWeekYear.cjs"}, "import": {"types": "./setISOWeekYear.d.ts", "default": "./setISOWeekYear.js"}}, "./setMilliseconds": {"require": {"types": "./setMilliseconds.d.cts", "default": "./setMilliseconds.cjs"}, "import": {"types": "./setMilliseconds.d.ts", "default": "./setMilliseconds.js"}}, "./setMinutes": {"require": {"types": "./setMinutes.d.cts", "default": "./setMinutes.cjs"}, "import": {"types": "./setMinutes.d.ts", "default": "./setMinutes.js"}}, "./setMonth": {"require": {"types": "./setMonth.d.cts", "default": "./setMonth.cjs"}, "import": {"types": "./setMonth.d.ts", "default": "./setMonth.js"}}, "./setQuarter": {"require": {"types": "./setQuarter.d.cts", "default": "./setQuarter.cjs"}, "import": {"types": "./setQuarter.d.ts", "default": "./setQuarter.js"}}, "./setSeconds": {"require": {"types": "./setSeconds.d.cts", "default": "./setSeconds.cjs"}, "import": {"types": "./setSeconds.d.ts", "default": "./setSeconds.js"}}, "./setWeek": {"require": {"types": "./setWeek.d.cts", "default": "./setWeek.cjs"}, "import": {"types": "./setWeek.d.ts", "default": "./setWeek.js"}}, "./setWeekYear": {"require": {"types": "./setWeekYear.d.cts", "default": "./setWeekYear.cjs"}, "import": {"types": "./setWeekYear.d.ts", "default": "./setWeekYear.js"}}, "./setYear": {"require": {"types": "./setYear.d.cts", "default": "./setYear.cjs"}, "import": {"types": "./setYear.d.ts", "default": "./setYear.js"}}, "./startOfDay": {"require": {"types": "./startOfDay.d.cts", "default": "./startOfDay.cjs"}, "import": {"types": "./startOfDay.d.ts", "default": "./startOfDay.js"}}, "./startOfDecade": {"require": {"types": "./startOfDecade.d.cts", "default": "./startOfDecade.cjs"}, "import": {"types": "./startOfDecade.d.ts", "default": "./startOfDecade.js"}}, "./startOfHour": {"require": {"types": "./startOfHour.d.cts", "default": "./startOfHour.cjs"}, "import": {"types": "./startOfHour.d.ts", "default": "./startOfHour.js"}}, "./startOfISOWeek": {"require": {"types": "./startOfISOWeek.d.cts", "default": "./startOfISOWeek.cjs"}, "import": {"types": "./startOfISOWeek.d.ts", "default": "./startOfISOWeek.js"}}, "./startOfISOWeekYear": {"require": {"types": "./startOfISOWeekYear.d.cts", "default": "./startOfISOWeekYear.cjs"}, "import": {"types": "./startOfISOWeekYear.d.ts", "default": "./startOfISOWeekYear.js"}}, "./startOfMinute": {"require": {"types": "./startOfMinute.d.cts", "default": "./startOfMinute.cjs"}, "import": {"types": "./startOfMinute.d.ts", "default": "./startOfMinute.js"}}, "./startOfMonth": {"require": {"types": "./startOfMonth.d.cts", "default": "./startOfMonth.cjs"}, "import": {"types": "./startOfMonth.d.ts", "default": "./startOfMonth.js"}}, "./startOfQuarter": {"require": {"types": "./startOfQuarter.d.cts", "default": "./startOfQuarter.cjs"}, "import": {"types": "./startOfQuarter.d.ts", "default": "./startOfQuarter.js"}}, "./startOfSecond": {"require": {"types": "./startOfSecond.d.cts", "default": "./startOfSecond.cjs"}, "import": {"types": "./startOfSecond.d.ts", "default": "./startOfSecond.js"}}, "./startOfToday": {"require": {"types": "./startOfToday.d.cts", "default": "./startOfToday.cjs"}, "import": {"types": "./startOfToday.d.ts", "default": "./startOfToday.js"}}, "./startOfTomorrow": {"require": {"types": "./startOfTomorrow.d.cts", "default": "./startOfTomorrow.cjs"}, "import": {"types": "./startOfTomorrow.d.ts", "default": "./startOfTomorrow.js"}}, "./startOfWeek": {"require": {"types": "./startOfWeek.d.cts", "default": "./startOfWeek.cjs"}, "import": {"types": "./startOfWeek.d.ts", "default": "./startOfWeek.js"}}, "./startOfWeekYear": {"require": {"types": "./startOfWeekYear.d.cts", "default": "./startOfWeekYear.cjs"}, "import": {"types": "./startOfWeekYear.d.ts", "default": "./startOfWeekYear.js"}}, "./startOfYear": {"require": {"types": "./startOfYear.d.cts", "default": "./startOfYear.cjs"}, "import": {"types": "./startOfYear.d.ts", "default": "./startOfYear.js"}}, "./startOfYesterday": {"require": {"types": "./startOfYesterday.d.cts", "default": "./startOfYesterday.cjs"}, "import": {"types": "./startOfYesterday.d.ts", "default": "./startOfYesterday.js"}}, "./sub": {"require": {"types": "./sub.d.cts", "default": "./sub.cjs"}, "import": {"types": "./sub.d.ts", "default": "./sub.js"}}, "./subBusinessDays": {"require": {"types": "./subBusinessDays.d.cts", "default": "./subBusinessDays.cjs"}, "import": {"types": "./subBusinessDays.d.ts", "default": "./subBusinessDays.js"}}, "./subDays": {"require": {"types": "./subDays.d.cts", "default": "./subDays.cjs"}, "import": {"types": "./subDays.d.ts", "default": "./subDays.js"}}, "./subHours": {"require": {"types": "./subHours.d.cts", "default": "./subHours.cjs"}, "import": {"types": "./subHours.d.ts", "default": "./subHours.js"}}, "./subISOWeekYears": {"require": {"types": "./subISOWeekYears.d.cts", "default": "./subISOWeekYears.cjs"}, "import": {"types": "./subISOWeekYears.d.ts", "default": "./subISOWeekYears.js"}}, "./subMilliseconds": {"require": {"types": "./subMilliseconds.d.cts", "default": "./subMilliseconds.cjs"}, "import": {"types": "./subMilliseconds.d.ts", "default": "./subMilliseconds.js"}}, "./subMinutes": {"require": {"types": "./subMinutes.d.cts", "default": "./subMinutes.cjs"}, "import": {"types": "./subMinutes.d.ts", "default": "./subMinutes.js"}}, "./subMonths": {"require": {"types": "./subMonths.d.cts", "default": "./subMonths.cjs"}, "import": {"types": "./subMonths.d.ts", "default": "./subMonths.js"}}, "./subQuarters": {"require": {"types": "./subQuarters.d.cts", "default": "./subQuarters.cjs"}, "import": {"types": "./subQuarters.d.ts", "default": "./subQuarters.js"}}, "./subSeconds": {"require": {"types": "./subSeconds.d.cts", "default": "./subSeconds.cjs"}, "import": {"types": "./subSeconds.d.ts", "default": "./subSeconds.js"}}, "./subWeeks": {"require": {"types": "./subWeeks.d.cts", "default": "./subWeeks.cjs"}, "import": {"types": "./subWeeks.d.ts", "default": "./subWeeks.js"}}, "./subYears": {"require": {"types": "./subYears.d.cts", "default": "./subYears.cjs"}, "import": {"types": "./subYears.d.ts", "default": "./subYears.js"}}, "./toDate": {"require": {"types": "./toDate.d.cts", "default": "./toDate.cjs"}, "import": {"types": "./toDate.d.ts", "default": "./toDate.js"}}, "./transpose": {"require": {"types": "./transpose.d.cts", "default": "./transpose.cjs"}, "import": {"types": "./transpose.d.ts", "default": "./transpose.js"}}, "./weeksToDays": {"require": {"types": "./weeksToDays.d.cts", "default": "./weeksToDays.cjs"}, "import": {"types": "./weeksToDays.d.ts", "default": "./weeksToDays.js"}}, "./yearsToDays": {"require": {"types": "./yearsToDays.d.cts", "default": "./yearsToDays.cjs"}, "import": {"types": "./yearsToDays.d.ts", "default": "./yearsToDays.js"}}, "./yearsToMonths": {"require": {"types": "./yearsToMonths.d.cts", "default": "./yearsToMonths.cjs"}, "import": {"types": "./yearsToMonths.d.ts", "default": "./yearsToMonths.js"}}, "./yearsToQuarters": {"require": {"types": "./yearsToQuarters.d.cts", "default": "./yearsToQuarters.cjs"}, "import": {"types": "./yearsToQuarters.d.ts", "default": "./yearsToQuarters.js"}}, "./fp/add": {"require": {"types": "./fp/add.d.cts", "default": "./fp/add.cjs"}, "import": {"types": "./fp/add.d.ts", "default": "./fp/add.js"}}, "./fp/addBusinessDays": {"require": {"types": "./fp/addBusinessDays.d.cts", "default": "./fp/addBusinessDays.cjs"}, "import": {"types": "./fp/addBusinessDays.d.ts", "default": "./fp/addBusinessDays.js"}}, "./fp/addBusinessDaysWithOptions": {"require": {"types": "./fp/addBusinessDaysWithOptions.d.cts", "default": "./fp/addBusinessDaysWithOptions.cjs"}, "import": {"types": "./fp/addBusinessDaysWithOptions.d.ts", "default": "./fp/addBusinessDaysWithOptions.js"}}, "./fp/addDays": {"require": {"types": "./fp/addDays.d.cts", "default": "./fp/addDays.cjs"}, "import": {"types": "./fp/addDays.d.ts", "default": "./fp/addDays.js"}}, "./fp/addDaysWithOptions": {"require": {"types": "./fp/addDaysWithOptions.d.cts", "default": "./fp/addDaysWithOptions.cjs"}, "import": {"types": "./fp/addDaysWithOptions.d.ts", "default": "./fp/addDaysWithOptions.js"}}, "./fp/addHours": {"require": {"types": "./fp/addHours.d.cts", "default": "./fp/addHours.cjs"}, "import": {"types": "./fp/addHours.d.ts", "default": "./fp/addHours.js"}}, "./fp/addHoursWithOptions": {"require": {"types": "./fp/addHoursWithOptions.d.cts", "default": "./fp/addHoursWithOptions.cjs"}, "import": {"types": "./fp/addHoursWithOptions.d.ts", "default": "./fp/addHoursWithOptions.js"}}, "./fp/addISOWeekYears": {"require": {"types": "./fp/addISOWeekYears.d.cts", "default": "./fp/addISOWeekYears.cjs"}, "import": {"types": "./fp/addISOWeekYears.d.ts", "default": "./fp/addISOWeekYears.js"}}, "./fp/addISOWeekYearsWithOptions": {"require": {"types": "./fp/addISOWeekYearsWithOptions.d.cts", "default": "./fp/addISOWeekYearsWithOptions.cjs"}, "import": {"types": "./fp/addISOWeekYearsWithOptions.d.ts", "default": "./fp/addISOWeekYearsWithOptions.js"}}, "./fp/addMilliseconds": {"require": {"types": "./fp/addMilliseconds.d.cts", "default": "./fp/addMilliseconds.cjs"}, "import": {"types": "./fp/addMilliseconds.d.ts", "default": "./fp/addMilliseconds.js"}}, "./fp/addMillisecondsWithOptions": {"require": {"types": "./fp/addMillisecondsWithOptions.d.cts", "default": "./fp/addMillisecondsWithOptions.cjs"}, "import": {"types": "./fp/addMillisecondsWithOptions.d.ts", "default": "./fp/addMillisecondsWithOptions.js"}}, "./fp/addMinutes": {"require": {"types": "./fp/addMinutes.d.cts", "default": "./fp/addMinutes.cjs"}, "import": {"types": "./fp/addMinutes.d.ts", "default": "./fp/addMinutes.js"}}, "./fp/addMinutesWithOptions": {"require": {"types": "./fp/addMinutesWithOptions.d.cts", "default": "./fp/addMinutesWithOptions.cjs"}, "import": {"types": "./fp/addMinutesWithOptions.d.ts", "default": "./fp/addMinutesWithOptions.js"}}, "./fp/addMonths": {"require": {"types": "./fp/addMonths.d.cts", "default": "./fp/addMonths.cjs"}, "import": {"types": "./fp/addMonths.d.ts", "default": "./fp/addMonths.js"}}, "./fp/addMonthsWithOptions": {"require": {"types": "./fp/addMonthsWithOptions.d.cts", "default": "./fp/addMonthsWithOptions.cjs"}, "import": {"types": "./fp/addMonthsWithOptions.d.ts", "default": "./fp/addMonthsWithOptions.js"}}, "./fp/addQuarters": {"require": {"types": "./fp/addQuarters.d.cts", "default": "./fp/addQuarters.cjs"}, "import": {"types": "./fp/addQuarters.d.ts", "default": "./fp/addQuarters.js"}}, "./fp/addQuartersWithOptions": {"require": {"types": "./fp/addQuartersWithOptions.d.cts", "default": "./fp/addQuartersWithOptions.cjs"}, "import": {"types": "./fp/addQuartersWithOptions.d.ts", "default": "./fp/addQuartersWithOptions.js"}}, "./fp/addSeconds": {"require": {"types": "./fp/addSeconds.d.cts", "default": "./fp/addSeconds.cjs"}, "import": {"types": "./fp/addSeconds.d.ts", "default": "./fp/addSeconds.js"}}, "./fp/addSecondsWithOptions": {"require": {"types": "./fp/addSecondsWithOptions.d.cts", "default": "./fp/addSecondsWithOptions.cjs"}, "import": {"types": "./fp/addSecondsWithOptions.d.ts", "default": "./fp/addSecondsWithOptions.js"}}, "./fp/addWeeks": {"require": {"types": "./fp/addWeeks.d.cts", "default": "./fp/addWeeks.cjs"}, "import": {"types": "./fp/addWeeks.d.ts", "default": "./fp/addWeeks.js"}}, "./fp/addWeeksWithOptions": {"require": {"types": "./fp/addWeeksWithOptions.d.cts", "default": "./fp/addWeeksWithOptions.cjs"}, "import": {"types": "./fp/addWeeksWithOptions.d.ts", "default": "./fp/addWeeksWithOptions.js"}}, "./fp/addWithOptions": {"require": {"types": "./fp/addWithOptions.d.cts", "default": "./fp/addWithOptions.cjs"}, "import": {"types": "./fp/addWithOptions.d.ts", "default": "./fp/addWithOptions.js"}}, "./fp/addYears": {"require": {"types": "./fp/addYears.d.cts", "default": "./fp/addYears.cjs"}, "import": {"types": "./fp/addYears.d.ts", "default": "./fp/addYears.js"}}, "./fp/addYearsWithOptions": {"require": {"types": "./fp/addYearsWithOptions.d.cts", "default": "./fp/addYearsWithOptions.cjs"}, "import": {"types": "./fp/addYearsWithOptions.d.ts", "default": "./fp/addYearsWithOptions.js"}}, "./fp/areIntervalsOverlapping": {"require": {"types": "./fp/areIntervalsOverlapping.d.cts", "default": "./fp/areIntervalsOverlapping.cjs"}, "import": {"types": "./fp/areIntervalsOverlapping.d.ts", "default": "./fp/areIntervalsOverlapping.js"}}, "./fp/areIntervalsOverlappingWithOptions": {"require": {"types": "./fp/areIntervalsOverlappingWithOptions.d.cts", "default": "./fp/areIntervalsOverlappingWithOptions.cjs"}, "import": {"types": "./fp/areIntervalsOverlappingWithOptions.d.ts", "default": "./fp/areIntervalsOverlappingWithOptions.js"}}, "./fp/clamp": {"require": {"types": "./fp/clamp.d.cts", "default": "./fp/clamp.cjs"}, "import": {"types": "./fp/clamp.d.ts", "default": "./fp/clamp.js"}}, "./fp/clampWithOptions": {"require": {"types": "./fp/clampWithOptions.d.cts", "default": "./fp/clampWithOptions.cjs"}, "import": {"types": "./fp/clampWithOptions.d.ts", "default": "./fp/clampWithOptions.js"}}, "./fp/closestIndexTo": {"require": {"types": "./fp/closestIndexTo.d.cts", "default": "./fp/closestIndexTo.cjs"}, "import": {"types": "./fp/closestIndexTo.d.ts", "default": "./fp/closestIndexTo.js"}}, "./fp/closestTo": {"require": {"types": "./fp/closestTo.d.cts", "default": "./fp/closestTo.cjs"}, "import": {"types": "./fp/closestTo.d.ts", "default": "./fp/closestTo.js"}}, "./fp/closestToWithOptions": {"require": {"types": "./fp/closestToWithOptions.d.cts", "default": "./fp/closestToWithOptions.cjs"}, "import": {"types": "./fp/closestToWithOptions.d.ts", "default": "./fp/closestToWithOptions.js"}}, "./fp/compareAsc": {"require": {"types": "./fp/compareAsc.d.cts", "default": "./fp/compareAsc.cjs"}, "import": {"types": "./fp/compareAsc.d.ts", "default": "./fp/compareAsc.js"}}, "./fp/compareDesc": {"require": {"types": "./fp/compareDesc.d.cts", "default": "./fp/compareDesc.cjs"}, "import": {"types": "./fp/compareDesc.d.ts", "default": "./fp/compareDesc.js"}}, "./fp/constructFrom": {"require": {"types": "./fp/constructFrom.d.cts", "default": "./fp/constructFrom.cjs"}, "import": {"types": "./fp/constructFrom.d.ts", "default": "./fp/constructFrom.js"}}, "./fp/daysToWeeks": {"require": {"types": "./fp/daysToWeeks.d.cts", "default": "./fp/daysToWeeks.cjs"}, "import": {"types": "./fp/daysToWeeks.d.ts", "default": "./fp/daysToWeeks.js"}}, "./fp/differenceInBusinessDays": {"require": {"types": "./fp/differenceInBusinessDays.d.cts", "default": "./fp/differenceInBusinessDays.cjs"}, "import": {"types": "./fp/differenceInBusinessDays.d.ts", "default": "./fp/differenceInBusinessDays.js"}}, "./fp/differenceInBusinessDaysWithOptions": {"require": {"types": "./fp/differenceInBusinessDaysWithOptions.d.cts", "default": "./fp/differenceInBusinessDaysWithOptions.cjs"}, "import": {"types": "./fp/differenceInBusinessDaysWithOptions.d.ts", "default": "./fp/differenceInBusinessDaysWithOptions.js"}}, "./fp/differenceInCalendarDays": {"require": {"types": "./fp/differenceInCalendarDays.d.cts", "default": "./fp/differenceInCalendarDays.cjs"}, "import": {"types": "./fp/differenceInCalendarDays.d.ts", "default": "./fp/differenceInCalendarDays.js"}}, "./fp/differenceInCalendarDaysWithOptions": {"require": {"types": "./fp/differenceInCalendarDaysWithOptions.d.cts", "default": "./fp/differenceInCalendarDaysWithOptions.cjs"}, "import": {"types": "./fp/differenceInCalendarDaysWithOptions.d.ts", "default": "./fp/differenceInCalendarDaysWithOptions.js"}}, "./fp/differenceInCalendarISOWeekYears": {"require": {"types": "./fp/differenceInCalendarISOWeekYears.d.cts", "default": "./fp/differenceInCalendarISOWeekYears.cjs"}, "import": {"types": "./fp/differenceInCalendarISOWeekYears.d.ts", "default": "./fp/differenceInCalendarISOWeekYears.js"}}, "./fp/differenceInCalendarISOWeekYearsWithOptions": {"require": {"types": "./fp/differenceInCalendarISOWeekYearsWithOptions.d.cts", "default": "./fp/differenceInCalendarISOWeekYearsWithOptions.cjs"}, "import": {"types": "./fp/differenceInCalendarISOWeekYearsWithOptions.d.ts", "default": "./fp/differenceInCalendarISOWeekYearsWithOptions.js"}}, "./fp/differenceInCalendarISOWeeks": {"require": {"types": "./fp/differenceInCalendarISOWeeks.d.cts", "default": "./fp/differenceInCalendarISOWeeks.cjs"}, "import": {"types": "./fp/differenceInCalendarISOWeeks.d.ts", "default": "./fp/differenceInCalendarISOWeeks.js"}}, "./fp/differenceInCalendarISOWeeksWithOptions": {"require": {"types": "./fp/differenceInCalendarISOWeeksWithOptions.d.cts", "default": "./fp/differenceInCalendarISOWeeksWithOptions.cjs"}, "import": {"types": "./fp/differenceInCalendarISOWeeksWithOptions.d.ts", "default": "./fp/differenceInCalendarISOWeeksWithOptions.js"}}, "./fp/differenceInCalendarMonths": {"require": {"types": "./fp/differenceInCalendarMonths.d.cts", "default": "./fp/differenceInCalendarMonths.cjs"}, "import": {"types": "./fp/differenceInCalendarMonths.d.ts", "default": "./fp/differenceInCalendarMonths.js"}}, "./fp/differenceInCalendarMonthsWithOptions": {"require": {"types": "./fp/differenceInCalendarMonthsWithOptions.d.cts", "default": "./fp/differenceInCalendarMonthsWithOptions.cjs"}, "import": {"types": "./fp/differenceInCalendarMonthsWithOptions.d.ts", "default": "./fp/differenceInCalendarMonthsWithOptions.js"}}, "./fp/differenceInCalendarQuarters": {"require": {"types": "./fp/differenceInCalendarQuarters.d.cts", "default": "./fp/differenceInCalendarQuarters.cjs"}, "import": {"types": "./fp/differenceInCalendarQuarters.d.ts", "default": "./fp/differenceInCalendarQuarters.js"}}, "./fp/differenceInCalendarQuartersWithOptions": {"require": {"types": "./fp/differenceInCalendarQuartersWithOptions.d.cts", "default": "./fp/differenceInCalendarQuartersWithOptions.cjs"}, "import": {"types": "./fp/differenceInCalendarQuartersWithOptions.d.ts", "default": "./fp/differenceInCalendarQuartersWithOptions.js"}}, "./fp/differenceInCalendarWeeks": {"require": {"types": "./fp/differenceInCalendarWeeks.d.cts", "default": "./fp/differenceInCalendarWeeks.cjs"}, "import": {"types": "./fp/differenceInCalendarWeeks.d.ts", "default": "./fp/differenceInCalendarWeeks.js"}}, "./fp/differenceInCalendarWeeksWithOptions": {"require": {"types": "./fp/differenceInCalendarWeeksWithOptions.d.cts", "default": "./fp/differenceInCalendarWeeksWithOptions.cjs"}, "import": {"types": "./fp/differenceInCalendarWeeksWithOptions.d.ts", "default": "./fp/differenceInCalendarWeeksWithOptions.js"}}, "./fp/differenceInCalendarYears": {"require": {"types": "./fp/differenceInCalendarYears.d.cts", "default": "./fp/differenceInCalendarYears.cjs"}, "import": {"types": "./fp/differenceInCalendarYears.d.ts", "default": "./fp/differenceInCalendarYears.js"}}, "./fp/differenceInCalendarYearsWithOptions": {"require": {"types": "./fp/differenceInCalendarYearsWithOptions.d.cts", "default": "./fp/differenceInCalendarYearsWithOptions.cjs"}, "import": {"types": "./fp/differenceInCalendarYearsWithOptions.d.ts", "default": "./fp/differenceInCalendarYearsWithOptions.js"}}, "./fp/differenceInDays": {"require": {"types": "./fp/differenceInDays.d.cts", "default": "./fp/differenceInDays.cjs"}, "import": {"types": "./fp/differenceInDays.d.ts", "default": "./fp/differenceInDays.js"}}, "./fp/differenceInDaysWithOptions": {"require": {"types": "./fp/differenceInDaysWithOptions.d.cts", "default": "./fp/differenceInDaysWithOptions.cjs"}, "import": {"types": "./fp/differenceInDaysWithOptions.d.ts", "default": "./fp/differenceInDaysWithOptions.js"}}, "./fp/differenceInHours": {"require": {"types": "./fp/differenceInHours.d.cts", "default": "./fp/differenceInHours.cjs"}, "import": {"types": "./fp/differenceInHours.d.ts", "default": "./fp/differenceInHours.js"}}, "./fp/differenceInHoursWithOptions": {"require": {"types": "./fp/differenceInHoursWithOptions.d.cts", "default": "./fp/differenceInHoursWithOptions.cjs"}, "import": {"types": "./fp/differenceInHoursWithOptions.d.ts", "default": "./fp/differenceInHoursWithOptions.js"}}, "./fp/differenceInISOWeekYears": {"require": {"types": "./fp/differenceInISOWeekYears.d.cts", "default": "./fp/differenceInISOWeekYears.cjs"}, "import": {"types": "./fp/differenceInISOWeekYears.d.ts", "default": "./fp/differenceInISOWeekYears.js"}}, "./fp/differenceInISOWeekYearsWithOptions": {"require": {"types": "./fp/differenceInISOWeekYearsWithOptions.d.cts", "default": "./fp/differenceInISOWeekYearsWithOptions.cjs"}, "import": {"types": "./fp/differenceInISOWeekYearsWithOptions.d.ts", "default": "./fp/differenceInISOWeekYearsWithOptions.js"}}, "./fp/differenceInMilliseconds": {"require": {"types": "./fp/differenceInMilliseconds.d.cts", "default": "./fp/differenceInMilliseconds.cjs"}, "import": {"types": "./fp/differenceInMilliseconds.d.ts", "default": "./fp/differenceInMilliseconds.js"}}, "./fp/differenceInMinutes": {"require": {"types": "./fp/differenceInMinutes.d.cts", "default": "./fp/differenceInMinutes.cjs"}, "import": {"types": "./fp/differenceInMinutes.d.ts", "default": "./fp/differenceInMinutes.js"}}, "./fp/differenceInMinutesWithOptions": {"require": {"types": "./fp/differenceInMinutesWithOptions.d.cts", "default": "./fp/differenceInMinutesWithOptions.cjs"}, "import": {"types": "./fp/differenceInMinutesWithOptions.d.ts", "default": "./fp/differenceInMinutesWithOptions.js"}}, "./fp/differenceInMonths": {"require": {"types": "./fp/differenceInMonths.d.cts", "default": "./fp/differenceInMonths.cjs"}, "import": {"types": "./fp/differenceInMonths.d.ts", "default": "./fp/differenceInMonths.js"}}, "./fp/differenceInMonthsWithOptions": {"require": {"types": "./fp/differenceInMonthsWithOptions.d.cts", "default": "./fp/differenceInMonthsWithOptions.cjs"}, "import": {"types": "./fp/differenceInMonthsWithOptions.d.ts", "default": "./fp/differenceInMonthsWithOptions.js"}}, "./fp/differenceInQuarters": {"require": {"types": "./fp/differenceInQuarters.d.cts", "default": "./fp/differenceInQuarters.cjs"}, "import": {"types": "./fp/differenceInQuarters.d.ts", "default": "./fp/differenceInQuarters.js"}}, "./fp/differenceInQuartersWithOptions": {"require": {"types": "./fp/differenceInQuartersWithOptions.d.cts", "default": "./fp/differenceInQuartersWithOptions.cjs"}, "import": {"types": "./fp/differenceInQuartersWithOptions.d.ts", "default": "./fp/differenceInQuartersWithOptions.js"}}, "./fp/differenceInSeconds": {"require": {"types": "./fp/differenceInSeconds.d.cts", "default": "./fp/differenceInSeconds.cjs"}, "import": {"types": "./fp/differenceInSeconds.d.ts", "default": "./fp/differenceInSeconds.js"}}, "./fp/differenceInSecondsWithOptions": {"require": {"types": "./fp/differenceInSecondsWithOptions.d.cts", "default": "./fp/differenceInSecondsWithOptions.cjs"}, "import": {"types": "./fp/differenceInSecondsWithOptions.d.ts", "default": "./fp/differenceInSecondsWithOptions.js"}}, "./fp/differenceInWeeks": {"require": {"types": "./fp/differenceInWeeks.d.cts", "default": "./fp/differenceInWeeks.cjs"}, "import": {"types": "./fp/differenceInWeeks.d.ts", "default": "./fp/differenceInWeeks.js"}}, "./fp/differenceInWeeksWithOptions": {"require": {"types": "./fp/differenceInWeeksWithOptions.d.cts", "default": "./fp/differenceInWeeksWithOptions.cjs"}, "import": {"types": "./fp/differenceInWeeksWithOptions.d.ts", "default": "./fp/differenceInWeeksWithOptions.js"}}, "./fp/differenceInYears": {"require": {"types": "./fp/differenceInYears.d.cts", "default": "./fp/differenceInYears.cjs"}, "import": {"types": "./fp/differenceInYears.d.ts", "default": "./fp/differenceInYears.js"}}, "./fp/differenceInYearsWithOptions": {"require": {"types": "./fp/differenceInYearsWithOptions.d.cts", "default": "./fp/differenceInYearsWithOptions.cjs"}, "import": {"types": "./fp/differenceInYearsWithOptions.d.ts", "default": "./fp/differenceInYearsWithOptions.js"}}, "./fp/eachDayOfInterval": {"require": {"types": "./fp/eachDayOfInterval.d.cts", "default": "./fp/eachDayOfInterval.cjs"}, "import": {"types": "./fp/eachDayOfInterval.d.ts", "default": "./fp/eachDayOfInterval.js"}}, "./fp/eachDayOfIntervalWithOptions": {"require": {"types": "./fp/eachDayOfIntervalWithOptions.d.cts", "default": "./fp/eachDayOfIntervalWithOptions.cjs"}, "import": {"types": "./fp/eachDayOfIntervalWithOptions.d.ts", "default": "./fp/eachDayOfIntervalWithOptions.js"}}, "./fp/eachHourOfInterval": {"require": {"types": "./fp/eachHourOfInterval.d.cts", "default": "./fp/eachHourOfInterval.cjs"}, "import": {"types": "./fp/eachHourOfInterval.d.ts", "default": "./fp/eachHourOfInterval.js"}}, "./fp/eachHourOfIntervalWithOptions": {"require": {"types": "./fp/eachHourOfIntervalWithOptions.d.cts", "default": "./fp/eachHourOfIntervalWithOptions.cjs"}, "import": {"types": "./fp/eachHourOfIntervalWithOptions.d.ts", "default": "./fp/eachHourOfIntervalWithOptions.js"}}, "./fp/eachMinuteOfInterval": {"require": {"types": "./fp/eachMinuteOfInterval.d.cts", "default": "./fp/eachMinuteOfInterval.cjs"}, "import": {"types": "./fp/eachMinuteOfInterval.d.ts", "default": "./fp/eachMinuteOfInterval.js"}}, "./fp/eachMinuteOfIntervalWithOptions": {"require": {"types": "./fp/eachMinuteOfIntervalWithOptions.d.cts", "default": "./fp/eachMinuteOfIntervalWithOptions.cjs"}, "import": {"types": "./fp/eachMinuteOfIntervalWithOptions.d.ts", "default": "./fp/eachMinuteOfIntervalWithOptions.js"}}, "./fp/eachMonthOfInterval": {"require": {"types": "./fp/eachMonthOfInterval.d.cts", "default": "./fp/eachMonthOfInterval.cjs"}, "import": {"types": "./fp/eachMonthOfInterval.d.ts", "default": "./fp/eachMonthOfInterval.js"}}, "./fp/eachMonthOfIntervalWithOptions": {"require": {"types": "./fp/eachMonthOfIntervalWithOptions.d.cts", "default": "./fp/eachMonthOfIntervalWithOptions.cjs"}, "import": {"types": "./fp/eachMonthOfIntervalWithOptions.d.ts", "default": "./fp/eachMonthOfIntervalWithOptions.js"}}, "./fp/eachQuarterOfInterval": {"require": {"types": "./fp/eachQuarterOfInterval.d.cts", "default": "./fp/eachQuarterOfInterval.cjs"}, "import": {"types": "./fp/eachQuarterOfInterval.d.ts", "default": "./fp/eachQuarterOfInterval.js"}}, "./fp/eachQuarterOfIntervalWithOptions": {"require": {"types": "./fp/eachQuarterOfIntervalWithOptions.d.cts", "default": "./fp/eachQuarterOfIntervalWithOptions.cjs"}, "import": {"types": "./fp/eachQuarterOfIntervalWithOptions.d.ts", "default": "./fp/eachQuarterOfIntervalWithOptions.js"}}, "./fp/eachWeekOfInterval": {"require": {"types": "./fp/eachWeekOfInterval.d.cts", "default": "./fp/eachWeekOfInterval.cjs"}, "import": {"types": "./fp/eachWeekOfInterval.d.ts", "default": "./fp/eachWeekOfInterval.js"}}, "./fp/eachWeekOfIntervalWithOptions": {"require": {"types": "./fp/eachWeekOfIntervalWithOptions.d.cts", "default": "./fp/eachWeekOfIntervalWithOptions.cjs"}, "import": {"types": "./fp/eachWeekOfIntervalWithOptions.d.ts", "default": "./fp/eachWeekOfIntervalWithOptions.js"}}, "./fp/eachWeekendOfInterval": {"require": {"types": "./fp/eachWeekendOfInterval.d.cts", "default": "./fp/eachWeekendOfInterval.cjs"}, "import": {"types": "./fp/eachWeekendOfInterval.d.ts", "default": "./fp/eachWeekendOfInterval.js"}}, "./fp/eachWeekendOfIntervalWithOptions": {"require": {"types": "./fp/eachWeekendOfIntervalWithOptions.d.cts", "default": "./fp/eachWeekendOfIntervalWithOptions.cjs"}, "import": {"types": "./fp/eachWeekendOfIntervalWithOptions.d.ts", "default": "./fp/eachWeekendOfIntervalWithOptions.js"}}, "./fp/eachWeekendOfMonth": {"require": {"types": "./fp/eachWeekendOfMonth.d.cts", "default": "./fp/eachWeekendOfMonth.cjs"}, "import": {"types": "./fp/eachWeekendOfMonth.d.ts", "default": "./fp/eachWeekendOfMonth.js"}}, "./fp/eachWeekendOfMonthWithOptions": {"require": {"types": "./fp/eachWeekendOfMonthWithOptions.d.cts", "default": "./fp/eachWeekendOfMonthWithOptions.cjs"}, "import": {"types": "./fp/eachWeekendOfMonthWithOptions.d.ts", "default": "./fp/eachWeekendOfMonthWithOptions.js"}}, "./fp/eachWeekendOfYear": {"require": {"types": "./fp/eachWeekendOfYear.d.cts", "default": "./fp/eachWeekendOfYear.cjs"}, "import": {"types": "./fp/eachWeekendOfYear.d.ts", "default": "./fp/eachWeekendOfYear.js"}}, "./fp/eachWeekendOfYearWithOptions": {"require": {"types": "./fp/eachWeekendOfYearWithOptions.d.cts", "default": "./fp/eachWeekendOfYearWithOptions.cjs"}, "import": {"types": "./fp/eachWeekendOfYearWithOptions.d.ts", "default": "./fp/eachWeekendOfYearWithOptions.js"}}, "./fp/eachYearOfInterval": {"require": {"types": "./fp/eachYearOfInterval.d.cts", "default": "./fp/eachYearOfInterval.cjs"}, "import": {"types": "./fp/eachYearOfInterval.d.ts", "default": "./fp/eachYearOfInterval.js"}}, "./fp/eachYearOfIntervalWithOptions": {"require": {"types": "./fp/eachYearOfIntervalWithOptions.d.cts", "default": "./fp/eachYearOfIntervalWithOptions.cjs"}, "import": {"types": "./fp/eachYearOfIntervalWithOptions.d.ts", "default": "./fp/eachYearOfIntervalWithOptions.js"}}, "./fp/endOfDay": {"require": {"types": "./fp/endOfDay.d.cts", "default": "./fp/endOfDay.cjs"}, "import": {"types": "./fp/endOfDay.d.ts", "default": "./fp/endOfDay.js"}}, "./fp/endOfDayWithOptions": {"require": {"types": "./fp/endOfDayWithOptions.d.cts", "default": "./fp/endOfDayWithOptions.cjs"}, "import": {"types": "./fp/endOfDayWithOptions.d.ts", "default": "./fp/endOfDayWithOptions.js"}}, "./fp/endOfDecade": {"require": {"types": "./fp/endOfDecade.d.cts", "default": "./fp/endOfDecade.cjs"}, "import": {"types": "./fp/endOfDecade.d.ts", "default": "./fp/endOfDecade.js"}}, "./fp/endOfDecadeWithOptions": {"require": {"types": "./fp/endOfDecadeWithOptions.d.cts", "default": "./fp/endOfDecadeWithOptions.cjs"}, "import": {"types": "./fp/endOfDecadeWithOptions.d.ts", "default": "./fp/endOfDecadeWithOptions.js"}}, "./fp/endOfHour": {"require": {"types": "./fp/endOfHour.d.cts", "default": "./fp/endOfHour.cjs"}, "import": {"types": "./fp/endOfHour.d.ts", "default": "./fp/endOfHour.js"}}, "./fp/endOfHourWithOptions": {"require": {"types": "./fp/endOfHourWithOptions.d.cts", "default": "./fp/endOfHourWithOptions.cjs"}, "import": {"types": "./fp/endOfHourWithOptions.d.ts", "default": "./fp/endOfHourWithOptions.js"}}, "./fp/endOfISOWeek": {"require": {"types": "./fp/endOfISOWeek.d.cts", "default": "./fp/endOfISOWeek.cjs"}, "import": {"types": "./fp/endOfISOWeek.d.ts", "default": "./fp/endOfISOWeek.js"}}, "./fp/endOfISOWeekWithOptions": {"require": {"types": "./fp/endOfISOWeekWithOptions.d.cts", "default": "./fp/endOfISOWeekWithOptions.cjs"}, "import": {"types": "./fp/endOfISOWeekWithOptions.d.ts", "default": "./fp/endOfISOWeekWithOptions.js"}}, "./fp/endOfISOWeekYear": {"require": {"types": "./fp/endOfISOWeekYear.d.cts", "default": "./fp/endOfISOWeekYear.cjs"}, "import": {"types": "./fp/endOfISOWeekYear.d.ts", "default": "./fp/endOfISOWeekYear.js"}}, "./fp/endOfISOWeekYearWithOptions": {"require": {"types": "./fp/endOfISOWeekYearWithOptions.d.cts", "default": "./fp/endOfISOWeekYearWithOptions.cjs"}, "import": {"types": "./fp/endOfISOWeekYearWithOptions.d.ts", "default": "./fp/endOfISOWeekYearWithOptions.js"}}, "./fp/endOfMinute": {"require": {"types": "./fp/endOfMinute.d.cts", "default": "./fp/endOfMinute.cjs"}, "import": {"types": "./fp/endOfMinute.d.ts", "default": "./fp/endOfMinute.js"}}, "./fp/endOfMinuteWithOptions": {"require": {"types": "./fp/endOfMinuteWithOptions.d.cts", "default": "./fp/endOfMinuteWithOptions.cjs"}, "import": {"types": "./fp/endOfMinuteWithOptions.d.ts", "default": "./fp/endOfMinuteWithOptions.js"}}, "./fp/endOfMonth": {"require": {"types": "./fp/endOfMonth.d.cts", "default": "./fp/endOfMonth.cjs"}, "import": {"types": "./fp/endOfMonth.d.ts", "default": "./fp/endOfMonth.js"}}, "./fp/endOfMonthWithOptions": {"require": {"types": "./fp/endOfMonthWithOptions.d.cts", "default": "./fp/endOfMonthWithOptions.cjs"}, "import": {"types": "./fp/endOfMonthWithOptions.d.ts", "default": "./fp/endOfMonthWithOptions.js"}}, "./fp/endOfQuarter": {"require": {"types": "./fp/endOfQuarter.d.cts", "default": "./fp/endOfQuarter.cjs"}, "import": {"types": "./fp/endOfQuarter.d.ts", "default": "./fp/endOfQuarter.js"}}, "./fp/endOfQuarterWithOptions": {"require": {"types": "./fp/endOfQuarterWithOptions.d.cts", "default": "./fp/endOfQuarterWithOptions.cjs"}, "import": {"types": "./fp/endOfQuarterWithOptions.d.ts", "default": "./fp/endOfQuarterWithOptions.js"}}, "./fp/endOfSecond": {"require": {"types": "./fp/endOfSecond.d.cts", "default": "./fp/endOfSecond.cjs"}, "import": {"types": "./fp/endOfSecond.d.ts", "default": "./fp/endOfSecond.js"}}, "./fp/endOfSecondWithOptions": {"require": {"types": "./fp/endOfSecondWithOptions.d.cts", "default": "./fp/endOfSecondWithOptions.cjs"}, "import": {"types": "./fp/endOfSecondWithOptions.d.ts", "default": "./fp/endOfSecondWithOptions.js"}}, "./fp/endOfWeek": {"require": {"types": "./fp/endOfWeek.d.cts", "default": "./fp/endOfWeek.cjs"}, "import": {"types": "./fp/endOfWeek.d.ts", "default": "./fp/endOfWeek.js"}}, "./fp/endOfWeekWithOptions": {"require": {"types": "./fp/endOfWeekWithOptions.d.cts", "default": "./fp/endOfWeekWithOptions.cjs"}, "import": {"types": "./fp/endOfWeekWithOptions.d.ts", "default": "./fp/endOfWeekWithOptions.js"}}, "./fp/endOfYear": {"require": {"types": "./fp/endOfYear.d.cts", "default": "./fp/endOfYear.cjs"}, "import": {"types": "./fp/endOfYear.d.ts", "default": "./fp/endOfYear.js"}}, "./fp/endOfYearWithOptions": {"require": {"types": "./fp/endOfYearWithOptions.d.cts", "default": "./fp/endOfYearWithOptions.cjs"}, "import": {"types": "./fp/endOfYearWithOptions.d.ts", "default": "./fp/endOfYearWithOptions.js"}}, "./fp/format": {"require": {"types": "./fp/format.d.cts", "default": "./fp/format.cjs"}, "import": {"types": "./fp/format.d.ts", "default": "./fp/format.js"}}, "./fp/formatDistance": {"require": {"types": "./fp/formatDistance.d.cts", "default": "./fp/formatDistance.cjs"}, "import": {"types": "./fp/formatDistance.d.ts", "default": "./fp/formatDistance.js"}}, "./fp/formatDistanceStrict": {"require": {"types": "./fp/formatDistanceStrict.d.cts", "default": "./fp/formatDistanceStrict.cjs"}, "import": {"types": "./fp/formatDistanceStrict.d.ts", "default": "./fp/formatDistanceStrict.js"}}, "./fp/formatDistanceStrictWithOptions": {"require": {"types": "./fp/formatDistanceStrictWithOptions.d.cts", "default": "./fp/formatDistanceStrictWithOptions.cjs"}, "import": {"types": "./fp/formatDistanceStrictWithOptions.d.ts", "default": "./fp/formatDistanceStrictWithOptions.js"}}, "./fp/formatDistanceWithOptions": {"require": {"types": "./fp/formatDistanceWithOptions.d.cts", "default": "./fp/formatDistanceWithOptions.cjs"}, "import": {"types": "./fp/formatDistanceWithOptions.d.ts", "default": "./fp/formatDistanceWithOptions.js"}}, "./fp/formatDuration": {"require": {"types": "./fp/formatDuration.d.cts", "default": "./fp/formatDuration.cjs"}, "import": {"types": "./fp/formatDuration.d.ts", "default": "./fp/formatDuration.js"}}, "./fp/formatDurationWithOptions": {"require": {"types": "./fp/formatDurationWithOptions.d.cts", "default": "./fp/formatDurationWithOptions.cjs"}, "import": {"types": "./fp/formatDurationWithOptions.d.ts", "default": "./fp/formatDurationWithOptions.js"}}, "./fp/formatISO": {"require": {"types": "./fp/formatISO.d.cts", "default": "./fp/formatISO.cjs"}, "import": {"types": "./fp/formatISO.d.ts", "default": "./fp/formatISO.js"}}, "./fp/formatISO9075": {"require": {"types": "./fp/formatISO9075.d.cts", "default": "./fp/formatISO9075.cjs"}, "import": {"types": "./fp/formatISO9075.d.ts", "default": "./fp/formatISO9075.js"}}, "./fp/formatISO9075WithOptions": {"require": {"types": "./fp/formatISO9075WithOptions.d.cts", "default": "./fp/formatISO9075WithOptions.cjs"}, "import": {"types": "./fp/formatISO9075WithOptions.d.ts", "default": "./fp/formatISO9075WithOptions.js"}}, "./fp/formatISODuration": {"require": {"types": "./fp/formatISODuration.d.cts", "default": "./fp/formatISODuration.cjs"}, "import": {"types": "./fp/formatISODuration.d.ts", "default": "./fp/formatISODuration.js"}}, "./fp/formatISOWithOptions": {"require": {"types": "./fp/formatISOWithOptions.d.cts", "default": "./fp/formatISOWithOptions.cjs"}, "import": {"types": "./fp/formatISOWithOptions.d.ts", "default": "./fp/formatISOWithOptions.js"}}, "./fp/formatRFC3339": {"require": {"types": "./fp/formatRFC3339.d.cts", "default": "./fp/formatRFC3339.cjs"}, "import": {"types": "./fp/formatRFC3339.d.ts", "default": "./fp/formatRFC3339.js"}}, "./fp/formatRFC3339WithOptions": {"require": {"types": "./fp/formatRFC3339WithOptions.d.cts", "default": "./fp/formatRFC3339WithOptions.cjs"}, "import": {"types": "./fp/formatRFC3339WithOptions.d.ts", "default": "./fp/formatRFC3339WithOptions.js"}}, "./fp/formatRFC7231": {"require": {"types": "./fp/formatRFC7231.d.cts", "default": "./fp/formatRFC7231.cjs"}, "import": {"types": "./fp/formatRFC7231.d.ts", "default": "./fp/formatRFC7231.js"}}, "./fp/formatRelative": {"require": {"types": "./fp/formatRelative.d.cts", "default": "./fp/formatRelative.cjs"}, "import": {"types": "./fp/formatRelative.d.ts", "default": "./fp/formatRelative.js"}}, "./fp/formatRelativeWithOptions": {"require": {"types": "./fp/formatRelativeWithOptions.d.cts", "default": "./fp/formatRelativeWithOptions.cjs"}, "import": {"types": "./fp/formatRelativeWithOptions.d.ts", "default": "./fp/formatRelativeWithOptions.js"}}, "./fp/formatWithOptions": {"require": {"types": "./fp/formatWithOptions.d.cts", "default": "./fp/formatWithOptions.cjs"}, "import": {"types": "./fp/formatWithOptions.d.ts", "default": "./fp/formatWithOptions.js"}}, "./fp/fromUnixTime": {"require": {"types": "./fp/fromUnixTime.d.cts", "default": "./fp/fromUnixTime.cjs"}, "import": {"types": "./fp/fromUnixTime.d.ts", "default": "./fp/fromUnixTime.js"}}, "./fp/fromUnixTimeWithOptions": {"require": {"types": "./fp/fromUnixTimeWithOptions.d.cts", "default": "./fp/fromUnixTimeWithOptions.cjs"}, "import": {"types": "./fp/fromUnixTimeWithOptions.d.ts", "default": "./fp/fromUnixTimeWithOptions.js"}}, "./fp/getDate": {"require": {"types": "./fp/getDate.d.cts", "default": "./fp/getDate.cjs"}, "import": {"types": "./fp/getDate.d.ts", "default": "./fp/getDate.js"}}, "./fp/getDateWithOptions": {"require": {"types": "./fp/getDateWithOptions.d.cts", "default": "./fp/getDateWithOptions.cjs"}, "import": {"types": "./fp/getDateWithOptions.d.ts", "default": "./fp/getDateWithOptions.js"}}, "./fp/getDay": {"require": {"types": "./fp/getDay.d.cts", "default": "./fp/getDay.cjs"}, "import": {"types": "./fp/getDay.d.ts", "default": "./fp/getDay.js"}}, "./fp/getDayOfYear": {"require": {"types": "./fp/getDayOfYear.d.cts", "default": "./fp/getDayOfYear.cjs"}, "import": {"types": "./fp/getDayOfYear.d.ts", "default": "./fp/getDayOfYear.js"}}, "./fp/getDayOfYearWithOptions": {"require": {"types": "./fp/getDayOfYearWithOptions.d.cts", "default": "./fp/getDayOfYearWithOptions.cjs"}, "import": {"types": "./fp/getDayOfYearWithOptions.d.ts", "default": "./fp/getDayOfYearWithOptions.js"}}, "./fp/getDayWithOptions": {"require": {"types": "./fp/getDayWithOptions.d.cts", "default": "./fp/getDayWithOptions.cjs"}, "import": {"types": "./fp/getDayWithOptions.d.ts", "default": "./fp/getDayWithOptions.js"}}, "./fp/getDaysInMonth": {"require": {"types": "./fp/getDaysInMonth.d.cts", "default": "./fp/getDaysInMonth.cjs"}, "import": {"types": "./fp/getDaysInMonth.d.ts", "default": "./fp/getDaysInMonth.js"}}, "./fp/getDaysInMonthWithOptions": {"require": {"types": "./fp/getDaysInMonthWithOptions.d.cts", "default": "./fp/getDaysInMonthWithOptions.cjs"}, "import": {"types": "./fp/getDaysInMonthWithOptions.d.ts", "default": "./fp/getDaysInMonthWithOptions.js"}}, "./fp/getDaysInYear": {"require": {"types": "./fp/getDaysInYear.d.cts", "default": "./fp/getDaysInYear.cjs"}, "import": {"types": "./fp/getDaysInYear.d.ts", "default": "./fp/getDaysInYear.js"}}, "./fp/getDaysInYearWithOptions": {"require": {"types": "./fp/getDaysInYearWithOptions.d.cts", "default": "./fp/getDaysInYearWithOptions.cjs"}, "import": {"types": "./fp/getDaysInYearWithOptions.d.ts", "default": "./fp/getDaysInYearWithOptions.js"}}, "./fp/getDecade": {"require": {"types": "./fp/getDecade.d.cts", "default": "./fp/getDecade.cjs"}, "import": {"types": "./fp/getDecade.d.ts", "default": "./fp/getDecade.js"}}, "./fp/getDecadeWithOptions": {"require": {"types": "./fp/getDecadeWithOptions.d.cts", "default": "./fp/getDecadeWithOptions.cjs"}, "import": {"types": "./fp/getDecadeWithOptions.d.ts", "default": "./fp/getDecadeWithOptions.js"}}, "./fp/getHours": {"require": {"types": "./fp/getHours.d.cts", "default": "./fp/getHours.cjs"}, "import": {"types": "./fp/getHours.d.ts", "default": "./fp/getHours.js"}}, "./fp/getHoursWithOptions": {"require": {"types": "./fp/getHoursWithOptions.d.cts", "default": "./fp/getHoursWithOptions.cjs"}, "import": {"types": "./fp/getHoursWithOptions.d.ts", "default": "./fp/getHoursWithOptions.js"}}, "./fp/getISODay": {"require": {"types": "./fp/getISODay.d.cts", "default": "./fp/getISODay.cjs"}, "import": {"types": "./fp/getISODay.d.ts", "default": "./fp/getISODay.js"}}, "./fp/getISODayWithOptions": {"require": {"types": "./fp/getISODayWithOptions.d.cts", "default": "./fp/getISODayWithOptions.cjs"}, "import": {"types": "./fp/getISODayWithOptions.d.ts", "default": "./fp/getISODayWithOptions.js"}}, "./fp/getISOWeek": {"require": {"types": "./fp/getISOWeek.d.cts", "default": "./fp/getISOWeek.cjs"}, "import": {"types": "./fp/getISOWeek.d.ts", "default": "./fp/getISOWeek.js"}}, "./fp/getISOWeekWithOptions": {"require": {"types": "./fp/getISOWeekWithOptions.d.cts", "default": "./fp/getISOWeekWithOptions.cjs"}, "import": {"types": "./fp/getISOWeekWithOptions.d.ts", "default": "./fp/getISOWeekWithOptions.js"}}, "./fp/getISOWeekYear": {"require": {"types": "./fp/getISOWeekYear.d.cts", "default": "./fp/getISOWeekYear.cjs"}, "import": {"types": "./fp/getISOWeekYear.d.ts", "default": "./fp/getISOWeekYear.js"}}, "./fp/getISOWeekYearWithOptions": {"require": {"types": "./fp/getISOWeekYearWithOptions.d.cts", "default": "./fp/getISOWeekYearWithOptions.cjs"}, "import": {"types": "./fp/getISOWeekYearWithOptions.d.ts", "default": "./fp/getISOWeekYearWithOptions.js"}}, "./fp/getISOWeeksInYear": {"require": {"types": "./fp/getISOWeeksInYear.d.cts", "default": "./fp/getISOWeeksInYear.cjs"}, "import": {"types": "./fp/getISOWeeksInYear.d.ts", "default": "./fp/getISOWeeksInYear.js"}}, "./fp/getISOWeeksInYearWithOptions": {"require": {"types": "./fp/getISOWeeksInYearWithOptions.d.cts", "default": "./fp/getISOWeeksInYearWithOptions.cjs"}, "import": {"types": "./fp/getISOWeeksInYearWithOptions.d.ts", "default": "./fp/getISOWeeksInYearWithOptions.js"}}, "./fp/getMilliseconds": {"require": {"types": "./fp/getMilliseconds.d.cts", "default": "./fp/getMilliseconds.cjs"}, "import": {"types": "./fp/getMilliseconds.d.ts", "default": "./fp/getMilliseconds.js"}}, "./fp/getMinutes": {"require": {"types": "./fp/getMinutes.d.cts", "default": "./fp/getMinutes.cjs"}, "import": {"types": "./fp/getMinutes.d.ts", "default": "./fp/getMinutes.js"}}, "./fp/getMinutesWithOptions": {"require": {"types": "./fp/getMinutesWithOptions.d.cts", "default": "./fp/getMinutesWithOptions.cjs"}, "import": {"types": "./fp/getMinutesWithOptions.d.ts", "default": "./fp/getMinutesWithOptions.js"}}, "./fp/getMonth": {"require": {"types": "./fp/getMonth.d.cts", "default": "./fp/getMonth.cjs"}, "import": {"types": "./fp/getMonth.d.ts", "default": "./fp/getMonth.js"}}, "./fp/getMonthWithOptions": {"require": {"types": "./fp/getMonthWithOptions.d.cts", "default": "./fp/getMonthWithOptions.cjs"}, "import": {"types": "./fp/getMonthWithOptions.d.ts", "default": "./fp/getMonthWithOptions.js"}}, "./fp/getOverlappingDaysInIntervals": {"require": {"types": "./fp/getOverlappingDaysInIntervals.d.cts", "default": "./fp/getOverlappingDaysInIntervals.cjs"}, "import": {"types": "./fp/getOverlappingDaysInIntervals.d.ts", "default": "./fp/getOverlappingDaysInIntervals.js"}}, "./fp/getQuarter": {"require": {"types": "./fp/getQuarter.d.cts", "default": "./fp/getQuarter.cjs"}, "import": {"types": "./fp/getQuarter.d.ts", "default": "./fp/getQuarter.js"}}, "./fp/getQuarterWithOptions": {"require": {"types": "./fp/getQuarterWithOptions.d.cts", "default": "./fp/getQuarterWithOptions.cjs"}, "import": {"types": "./fp/getQuarterWithOptions.d.ts", "default": "./fp/getQuarterWithOptions.js"}}, "./fp/getSeconds": {"require": {"types": "./fp/getSeconds.d.cts", "default": "./fp/getSeconds.cjs"}, "import": {"types": "./fp/getSeconds.d.ts", "default": "./fp/getSeconds.js"}}, "./fp/getTime": {"require": {"types": "./fp/getTime.d.cts", "default": "./fp/getTime.cjs"}, "import": {"types": "./fp/getTime.d.ts", "default": "./fp/getTime.js"}}, "./fp/getUnixTime": {"require": {"types": "./fp/getUnixTime.d.cts", "default": "./fp/getUnixTime.cjs"}, "import": {"types": "./fp/getUnixTime.d.ts", "default": "./fp/getUnixTime.js"}}, "./fp/getWeek": {"require": {"types": "./fp/getWeek.d.cts", "default": "./fp/getWeek.cjs"}, "import": {"types": "./fp/getWeek.d.ts", "default": "./fp/getWeek.js"}}, "./fp/getWeekOfMonth": {"require": {"types": "./fp/getWeekOfMonth.d.cts", "default": "./fp/getWeekOfMonth.cjs"}, "import": {"types": "./fp/getWeekOfMonth.d.ts", "default": "./fp/getWeekOfMonth.js"}}, "./fp/getWeekOfMonthWithOptions": {"require": {"types": "./fp/getWeekOfMonthWithOptions.d.cts", "default": "./fp/getWeekOfMonthWithOptions.cjs"}, "import": {"types": "./fp/getWeekOfMonthWithOptions.d.ts", "default": "./fp/getWeekOfMonthWithOptions.js"}}, "./fp/getWeekWithOptions": {"require": {"types": "./fp/getWeekWithOptions.d.cts", "default": "./fp/getWeekWithOptions.cjs"}, "import": {"types": "./fp/getWeekWithOptions.d.ts", "default": "./fp/getWeekWithOptions.js"}}, "./fp/getWeekYear": {"require": {"types": "./fp/getWeekYear.d.cts", "default": "./fp/getWeekYear.cjs"}, "import": {"types": "./fp/getWeekYear.d.ts", "default": "./fp/getWeekYear.js"}}, "./fp/getWeekYearWithOptions": {"require": {"types": "./fp/getWeekYearWithOptions.d.cts", "default": "./fp/getWeekYearWithOptions.cjs"}, "import": {"types": "./fp/getWeekYearWithOptions.d.ts", "default": "./fp/getWeekYearWithOptions.js"}}, "./fp/getWeeksInMonth": {"require": {"types": "./fp/getWeeksInMonth.d.cts", "default": "./fp/getWeeksInMonth.cjs"}, "import": {"types": "./fp/getWeeksInMonth.d.ts", "default": "./fp/getWeeksInMonth.js"}}, "./fp/getWeeksInMonthWithOptions": {"require": {"types": "./fp/getWeeksInMonthWithOptions.d.cts", "default": "./fp/getWeeksInMonthWithOptions.cjs"}, "import": {"types": "./fp/getWeeksInMonthWithOptions.d.ts", "default": "./fp/getWeeksInMonthWithOptions.js"}}, "./fp/getYear": {"require": {"types": "./fp/getYear.d.cts", "default": "./fp/getYear.cjs"}, "import": {"types": "./fp/getYear.d.ts", "default": "./fp/getYear.js"}}, "./fp/getYearWithOptions": {"require": {"types": "./fp/getYearWithOptions.d.cts", "default": "./fp/getYearWithOptions.cjs"}, "import": {"types": "./fp/getYearWithOptions.d.ts", "default": "./fp/getYearWithOptions.js"}}, "./fp/hoursToMilliseconds": {"require": {"types": "./fp/hoursToMilliseconds.d.cts", "default": "./fp/hoursToMilliseconds.cjs"}, "import": {"types": "./fp/hoursToMilliseconds.d.ts", "default": "./fp/hoursToMilliseconds.js"}}, "./fp/hoursToMinutes": {"require": {"types": "./fp/hoursToMinutes.d.cts", "default": "./fp/hoursToMinutes.cjs"}, "import": {"types": "./fp/hoursToMinutes.d.ts", "default": "./fp/hoursToMinutes.js"}}, "./fp/hoursToSeconds": {"require": {"types": "./fp/hoursToSeconds.d.cts", "default": "./fp/hoursToSeconds.cjs"}, "import": {"types": "./fp/hoursToSeconds.d.ts", "default": "./fp/hoursToSeconds.js"}}, "./fp/interval": {"require": {"types": "./fp/interval.d.cts", "default": "./fp/interval.cjs"}, "import": {"types": "./fp/interval.d.ts", "default": "./fp/interval.js"}}, "./fp/intervalToDuration": {"require": {"types": "./fp/intervalToDuration.d.cts", "default": "./fp/intervalToDuration.cjs"}, "import": {"types": "./fp/intervalToDuration.d.ts", "default": "./fp/intervalToDuration.js"}}, "./fp/intervalToDurationWithOptions": {"require": {"types": "./fp/intervalToDurationWithOptions.d.cts", "default": "./fp/intervalToDurationWithOptions.cjs"}, "import": {"types": "./fp/intervalToDurationWithOptions.d.ts", "default": "./fp/intervalToDurationWithOptions.js"}}, "./fp/intervalWithOptions": {"require": {"types": "./fp/intervalWithOptions.d.cts", "default": "./fp/intervalWithOptions.cjs"}, "import": {"types": "./fp/intervalWithOptions.d.ts", "default": "./fp/intervalWithOptions.js"}}, "./fp/intlFormat": {"require": {"types": "./fp/intlFormat.d.cts", "default": "./fp/intlFormat.cjs"}, "import": {"types": "./fp/intlFormat.d.ts", "default": "./fp/intlFormat.js"}}, "./fp/intlFormatDistance": {"require": {"types": "./fp/intlFormatDistance.d.cts", "default": "./fp/intlFormatDistance.cjs"}, "import": {"types": "./fp/intlFormatDistance.d.ts", "default": "./fp/intlFormatDistance.js"}}, "./fp/intlFormatDistanceWithOptions": {"require": {"types": "./fp/intlFormatDistanceWithOptions.d.cts", "default": "./fp/intlFormatDistanceWithOptions.cjs"}, "import": {"types": "./fp/intlFormatDistanceWithOptions.d.ts", "default": "./fp/intlFormatDistanceWithOptions.js"}}, "./fp/isAfter": {"require": {"types": "./fp/isAfter.d.cts", "default": "./fp/isAfter.cjs"}, "import": {"types": "./fp/isAfter.d.ts", "default": "./fp/isAfter.js"}}, "./fp/isBefore": {"require": {"types": "./fp/isBefore.d.cts", "default": "./fp/isBefore.cjs"}, "import": {"types": "./fp/isBefore.d.ts", "default": "./fp/isBefore.js"}}, "./fp/isDate": {"require": {"types": "./fp/isDate.d.cts", "default": "./fp/isDate.cjs"}, "import": {"types": "./fp/isDate.d.ts", "default": "./fp/isDate.js"}}, "./fp/isEqual": {"require": {"types": "./fp/isEqual.d.cts", "default": "./fp/isEqual.cjs"}, "import": {"types": "./fp/isEqual.d.ts", "default": "./fp/isEqual.js"}}, "./fp/isExists": {"require": {"types": "./fp/isExists.d.cts", "default": "./fp/isExists.cjs"}, "import": {"types": "./fp/isExists.d.ts", "default": "./fp/isExists.js"}}, "./fp/isFirstDayOfMonth": {"require": {"types": "./fp/isFirstDayOfMonth.d.cts", "default": "./fp/isFirstDayOfMonth.cjs"}, "import": {"types": "./fp/isFirstDayOfMonth.d.ts", "default": "./fp/isFirstDayOfMonth.js"}}, "./fp/isFirstDayOfMonthWithOptions": {"require": {"types": "./fp/isFirstDayOfMonthWithOptions.d.cts", "default": "./fp/isFirstDayOfMonthWithOptions.cjs"}, "import": {"types": "./fp/isFirstDayOfMonthWithOptions.d.ts", "default": "./fp/isFirstDayOfMonthWithOptions.js"}}, "./fp/isFriday": {"require": {"types": "./fp/isFriday.d.cts", "default": "./fp/isFriday.cjs"}, "import": {"types": "./fp/isFriday.d.ts", "default": "./fp/isFriday.js"}}, "./fp/isFridayWithOptions": {"require": {"types": "./fp/isFridayWithOptions.d.cts", "default": "./fp/isFridayWithOptions.cjs"}, "import": {"types": "./fp/isFridayWithOptions.d.ts", "default": "./fp/isFridayWithOptions.js"}}, "./fp/isLastDayOfMonth": {"require": {"types": "./fp/isLastDayOfMonth.d.cts", "default": "./fp/isLastDayOfMonth.cjs"}, "import": {"types": "./fp/isLastDayOfMonth.d.ts", "default": "./fp/isLastDayOfMonth.js"}}, "./fp/isLastDayOfMonthWithOptions": {"require": {"types": "./fp/isLastDayOfMonthWithOptions.d.cts", "default": "./fp/isLastDayOfMonthWithOptions.cjs"}, "import": {"types": "./fp/isLastDayOfMonthWithOptions.d.ts", "default": "./fp/isLastDayOfMonthWithOptions.js"}}, "./fp/isLeapYear": {"require": {"types": "./fp/isLeapYear.d.cts", "default": "./fp/isLeapYear.cjs"}, "import": {"types": "./fp/isLeapYear.d.ts", "default": "./fp/isLeapYear.js"}}, "./fp/isLeapYearWithOptions": {"require": {"types": "./fp/isLeapYearWithOptions.d.cts", "default": "./fp/isLeapYearWithOptions.cjs"}, "import": {"types": "./fp/isLeapYearWithOptions.d.ts", "default": "./fp/isLeapYearWithOptions.js"}}, "./fp/isMatch": {"require": {"types": "./fp/isMatch.d.cts", "default": "./fp/isMatch.cjs"}, "import": {"types": "./fp/isMatch.d.ts", "default": "./fp/isMatch.js"}}, "./fp/isMatchWithOptions": {"require": {"types": "./fp/isMatchWithOptions.d.cts", "default": "./fp/isMatchWithOptions.cjs"}, "import": {"types": "./fp/isMatchWithOptions.d.ts", "default": "./fp/isMatchWithOptions.js"}}, "./fp/isMonday": {"require": {"types": "./fp/isMonday.d.cts", "default": "./fp/isMonday.cjs"}, "import": {"types": "./fp/isMonday.d.ts", "default": "./fp/isMonday.js"}}, "./fp/isMondayWithOptions": {"require": {"types": "./fp/isMondayWithOptions.d.cts", "default": "./fp/isMondayWithOptions.cjs"}, "import": {"types": "./fp/isMondayWithOptions.d.ts", "default": "./fp/isMondayWithOptions.js"}}, "./fp/isSameDay": {"require": {"types": "./fp/isSameDay.d.cts", "default": "./fp/isSameDay.cjs"}, "import": {"types": "./fp/isSameDay.d.ts", "default": "./fp/isSameDay.js"}}, "./fp/isSameDayWithOptions": {"require": {"types": "./fp/isSameDayWithOptions.d.cts", "default": "./fp/isSameDayWithOptions.cjs"}, "import": {"types": "./fp/isSameDayWithOptions.d.ts", "default": "./fp/isSameDayWithOptions.js"}}, "./fp/isSameHour": {"require": {"types": "./fp/isSameHour.d.cts", "default": "./fp/isSameHour.cjs"}, "import": {"types": "./fp/isSameHour.d.ts", "default": "./fp/isSameHour.js"}}, "./fp/isSameHourWithOptions": {"require": {"types": "./fp/isSameHourWithOptions.d.cts", "default": "./fp/isSameHourWithOptions.cjs"}, "import": {"types": "./fp/isSameHourWithOptions.d.ts", "default": "./fp/isSameHourWithOptions.js"}}, "./fp/isSameISOWeek": {"require": {"types": "./fp/isSameISOWeek.d.cts", "default": "./fp/isSameISOWeek.cjs"}, "import": {"types": "./fp/isSameISOWeek.d.ts", "default": "./fp/isSameISOWeek.js"}}, "./fp/isSameISOWeekWithOptions": {"require": {"types": "./fp/isSameISOWeekWithOptions.d.cts", "default": "./fp/isSameISOWeekWithOptions.cjs"}, "import": {"types": "./fp/isSameISOWeekWithOptions.d.ts", "default": "./fp/isSameISOWeekWithOptions.js"}}, "./fp/isSameISOWeekYear": {"require": {"types": "./fp/isSameISOWeekYear.d.cts", "default": "./fp/isSameISOWeekYear.cjs"}, "import": {"types": "./fp/isSameISOWeekYear.d.ts", "default": "./fp/isSameISOWeekYear.js"}}, "./fp/isSameISOWeekYearWithOptions": {"require": {"types": "./fp/isSameISOWeekYearWithOptions.d.cts", "default": "./fp/isSameISOWeekYearWithOptions.cjs"}, "import": {"types": "./fp/isSameISOWeekYearWithOptions.d.ts", "default": "./fp/isSameISOWeekYearWithOptions.js"}}, "./fp/isSameMinute": {"require": {"types": "./fp/isSameMinute.d.cts", "default": "./fp/isSameMinute.cjs"}, "import": {"types": "./fp/isSameMinute.d.ts", "default": "./fp/isSameMinute.js"}}, "./fp/isSameMonth": {"require": {"types": "./fp/isSameMonth.d.cts", "default": "./fp/isSameMonth.cjs"}, "import": {"types": "./fp/isSameMonth.d.ts", "default": "./fp/isSameMonth.js"}}, "./fp/isSameMonthWithOptions": {"require": {"types": "./fp/isSameMonthWithOptions.d.cts", "default": "./fp/isSameMonthWithOptions.cjs"}, "import": {"types": "./fp/isSameMonthWithOptions.d.ts", "default": "./fp/isSameMonthWithOptions.js"}}, "./fp/isSameQuarter": {"require": {"types": "./fp/isSameQuarter.d.cts", "default": "./fp/isSameQuarter.cjs"}, "import": {"types": "./fp/isSameQuarter.d.ts", "default": "./fp/isSameQuarter.js"}}, "./fp/isSameQuarterWithOptions": {"require": {"types": "./fp/isSameQuarterWithOptions.d.cts", "default": "./fp/isSameQuarterWithOptions.cjs"}, "import": {"types": "./fp/isSameQuarterWithOptions.d.ts", "default": "./fp/isSameQuarterWithOptions.js"}}, "./fp/isSameSecond": {"require": {"types": "./fp/isSameSecond.d.cts", "default": "./fp/isSameSecond.cjs"}, "import": {"types": "./fp/isSameSecond.d.ts", "default": "./fp/isSameSecond.js"}}, "./fp/isSameWeek": {"require": {"types": "./fp/isSameWeek.d.cts", "default": "./fp/isSameWeek.cjs"}, "import": {"types": "./fp/isSameWeek.d.ts", "default": "./fp/isSameWeek.js"}}, "./fp/isSameWeekWithOptions": {"require": {"types": "./fp/isSameWeekWithOptions.d.cts", "default": "./fp/isSameWeekWithOptions.cjs"}, "import": {"types": "./fp/isSameWeekWithOptions.d.ts", "default": "./fp/isSameWeekWithOptions.js"}}, "./fp/isSameYear": {"require": {"types": "./fp/isSameYear.d.cts", "default": "./fp/isSameYear.cjs"}, "import": {"types": "./fp/isSameYear.d.ts", "default": "./fp/isSameYear.js"}}, "./fp/isSameYearWithOptions": {"require": {"types": "./fp/isSameYearWithOptions.d.cts", "default": "./fp/isSameYearWithOptions.cjs"}, "import": {"types": "./fp/isSameYearWithOptions.d.ts", "default": "./fp/isSameYearWithOptions.js"}}, "./fp/isSaturday": {"require": {"types": "./fp/isSaturday.d.cts", "default": "./fp/isSaturday.cjs"}, "import": {"types": "./fp/isSaturday.d.ts", "default": "./fp/isSaturday.js"}}, "./fp/isSaturdayWithOptions": {"require": {"types": "./fp/isSaturdayWithOptions.d.cts", "default": "./fp/isSaturdayWithOptions.cjs"}, "import": {"types": "./fp/isSaturdayWithOptions.d.ts", "default": "./fp/isSaturdayWithOptions.js"}}, "./fp/isSunday": {"require": {"types": "./fp/isSunday.d.cts", "default": "./fp/isSunday.cjs"}, "import": {"types": "./fp/isSunday.d.ts", "default": "./fp/isSunday.js"}}, "./fp/isSundayWithOptions": {"require": {"types": "./fp/isSundayWithOptions.d.cts", "default": "./fp/isSundayWithOptions.cjs"}, "import": {"types": "./fp/isSundayWithOptions.d.ts", "default": "./fp/isSundayWithOptions.js"}}, "./fp/isThursday": {"require": {"types": "./fp/isThursday.d.cts", "default": "./fp/isThursday.cjs"}, "import": {"types": "./fp/isThursday.d.ts", "default": "./fp/isThursday.js"}}, "./fp/isThursdayWithOptions": {"require": {"types": "./fp/isThursdayWithOptions.d.cts", "default": "./fp/isThursdayWithOptions.cjs"}, "import": {"types": "./fp/isThursdayWithOptions.d.ts", "default": "./fp/isThursdayWithOptions.js"}}, "./fp/isTuesday": {"require": {"types": "./fp/isTuesday.d.cts", "default": "./fp/isTuesday.cjs"}, "import": {"types": "./fp/isTuesday.d.ts", "default": "./fp/isTuesday.js"}}, "./fp/isTuesdayWithOptions": {"require": {"types": "./fp/isTuesdayWithOptions.d.cts", "default": "./fp/isTuesdayWithOptions.cjs"}, "import": {"types": "./fp/isTuesdayWithOptions.d.ts", "default": "./fp/isTuesdayWithOptions.js"}}, "./fp/isValid": {"require": {"types": "./fp/isValid.d.cts", "default": "./fp/isValid.cjs"}, "import": {"types": "./fp/isValid.d.ts", "default": "./fp/isValid.js"}}, "./fp/isWednesday": {"require": {"types": "./fp/isWednesday.d.cts", "default": "./fp/isWednesday.cjs"}, "import": {"types": "./fp/isWednesday.d.ts", "default": "./fp/isWednesday.js"}}, "./fp/isWednesdayWithOptions": {"require": {"types": "./fp/isWednesdayWithOptions.d.cts", "default": "./fp/isWednesdayWithOptions.cjs"}, "import": {"types": "./fp/isWednesdayWithOptions.d.ts", "default": "./fp/isWednesdayWithOptions.js"}}, "./fp/isWeekend": {"require": {"types": "./fp/isWeekend.d.cts", "default": "./fp/isWeekend.cjs"}, "import": {"types": "./fp/isWeekend.d.ts", "default": "./fp/isWeekend.js"}}, "./fp/isWeekendWithOptions": {"require": {"types": "./fp/isWeekendWithOptions.d.cts", "default": "./fp/isWeekendWithOptions.cjs"}, "import": {"types": "./fp/isWeekendWithOptions.d.ts", "default": "./fp/isWeekendWithOptions.js"}}, "./fp/isWithinInterval": {"require": {"types": "./fp/isWithinInterval.d.cts", "default": "./fp/isWithinInterval.cjs"}, "import": {"types": "./fp/isWithinInterval.d.ts", "default": "./fp/isWithinInterval.js"}}, "./fp/isWithinIntervalWithOptions": {"require": {"types": "./fp/isWithinIntervalWithOptions.d.cts", "default": "./fp/isWithinIntervalWithOptions.cjs"}, "import": {"types": "./fp/isWithinIntervalWithOptions.d.ts", "default": "./fp/isWithinIntervalWithOptions.js"}}, "./fp/lastDayOfDecade": {"require": {"types": "./fp/lastDayOfDecade.d.cts", "default": "./fp/lastDayOfDecade.cjs"}, "import": {"types": "./fp/lastDayOfDecade.d.ts", "default": "./fp/lastDayOfDecade.js"}}, "./fp/lastDayOfDecadeWithOptions": {"require": {"types": "./fp/lastDayOfDecadeWithOptions.d.cts", "default": "./fp/lastDayOfDecadeWithOptions.cjs"}, "import": {"types": "./fp/lastDayOfDecadeWithOptions.d.ts", "default": "./fp/lastDayOfDecadeWithOptions.js"}}, "./fp/lastDayOfISOWeek": {"require": {"types": "./fp/lastDayOfISOWeek.d.cts", "default": "./fp/lastDayOfISOWeek.cjs"}, "import": {"types": "./fp/lastDayOfISOWeek.d.ts", "default": "./fp/lastDayOfISOWeek.js"}}, "./fp/lastDayOfISOWeekWithOptions": {"require": {"types": "./fp/lastDayOfISOWeekWithOptions.d.cts", "default": "./fp/lastDayOfISOWeekWithOptions.cjs"}, "import": {"types": "./fp/lastDayOfISOWeekWithOptions.d.ts", "default": "./fp/lastDayOfISOWeekWithOptions.js"}}, "./fp/lastDayOfISOWeekYear": {"require": {"types": "./fp/lastDayOfISOWeekYear.d.cts", "default": "./fp/lastDayOfISOWeekYear.cjs"}, "import": {"types": "./fp/lastDayOfISOWeekYear.d.ts", "default": "./fp/lastDayOfISOWeekYear.js"}}, "./fp/lastDayOfISOWeekYearWithOptions": {"require": {"types": "./fp/lastDayOfISOWeekYearWithOptions.d.cts", "default": "./fp/lastDayOfISOWeekYearWithOptions.cjs"}, "import": {"types": "./fp/lastDayOfISOWeekYearWithOptions.d.ts", "default": "./fp/lastDayOfISOWeekYearWithOptions.js"}}, "./fp/lastDayOfMonth": {"require": {"types": "./fp/lastDayOfMonth.d.cts", "default": "./fp/lastDayOfMonth.cjs"}, "import": {"types": "./fp/lastDayOfMonth.d.ts", "default": "./fp/lastDayOfMonth.js"}}, "./fp/lastDayOfMonthWithOptions": {"require": {"types": "./fp/lastDayOfMonthWithOptions.d.cts", "default": "./fp/lastDayOfMonthWithOptions.cjs"}, "import": {"types": "./fp/lastDayOfMonthWithOptions.d.ts", "default": "./fp/lastDayOfMonthWithOptions.js"}}, "./fp/lastDayOfQuarter": {"require": {"types": "./fp/lastDayOfQuarter.d.cts", "default": "./fp/lastDayOfQuarter.cjs"}, "import": {"types": "./fp/lastDayOfQuarter.d.ts", "default": "./fp/lastDayOfQuarter.js"}}, "./fp/lastDayOfQuarterWithOptions": {"require": {"types": "./fp/lastDayOfQuarterWithOptions.d.cts", "default": "./fp/lastDayOfQuarterWithOptions.cjs"}, "import": {"types": "./fp/lastDayOfQuarterWithOptions.d.ts", "default": "./fp/lastDayOfQuarterWithOptions.js"}}, "./fp/lastDayOfWeek": {"require": {"types": "./fp/lastDayOfWeek.d.cts", "default": "./fp/lastDayOfWeek.cjs"}, "import": {"types": "./fp/lastDayOfWeek.d.ts", "default": "./fp/lastDayOfWeek.js"}}, "./fp/lastDayOfWeekWithOptions": {"require": {"types": "./fp/lastDayOfWeekWithOptions.d.cts", "default": "./fp/lastDayOfWeekWithOptions.cjs"}, "import": {"types": "./fp/lastDayOfWeekWithOptions.d.ts", "default": "./fp/lastDayOfWeekWithOptions.js"}}, "./fp/lastDayOfYear": {"require": {"types": "./fp/lastDayOfYear.d.cts", "default": "./fp/lastDayOfYear.cjs"}, "import": {"types": "./fp/lastDayOfYear.d.ts", "default": "./fp/lastDayOfYear.js"}}, "./fp/lastDayOfYearWithOptions": {"require": {"types": "./fp/lastDayOfYearWithOptions.d.cts", "default": "./fp/lastDayOfYearWithOptions.cjs"}, "import": {"types": "./fp/lastDayOfYearWithOptions.d.ts", "default": "./fp/lastDayOfYearWithOptions.js"}}, "./fp/lightFormat": {"require": {"types": "./fp/lightFormat.d.cts", "default": "./fp/lightFormat.cjs"}, "import": {"types": "./fp/lightFormat.d.ts", "default": "./fp/lightFormat.js"}}, "./fp/max": {"require": {"types": "./fp/max.d.cts", "default": "./fp/max.cjs"}, "import": {"types": "./fp/max.d.ts", "default": "./fp/max.js"}}, "./fp/maxWithOptions": {"require": {"types": "./fp/maxWithOptions.d.cts", "default": "./fp/maxWithOptions.cjs"}, "import": {"types": "./fp/maxWithOptions.d.ts", "default": "./fp/maxWithOptions.js"}}, "./fp/milliseconds": {"require": {"types": "./fp/milliseconds.d.cts", "default": "./fp/milliseconds.cjs"}, "import": {"types": "./fp/milliseconds.d.ts", "default": "./fp/milliseconds.js"}}, "./fp/millisecondsToHours": {"require": {"types": "./fp/millisecondsToHours.d.cts", "default": "./fp/millisecondsToHours.cjs"}, "import": {"types": "./fp/millisecondsToHours.d.ts", "default": "./fp/millisecondsToHours.js"}}, "./fp/millisecondsToMinutes": {"require": {"types": "./fp/millisecondsToMinutes.d.cts", "default": "./fp/millisecondsToMinutes.cjs"}, "import": {"types": "./fp/millisecondsToMinutes.d.ts", "default": "./fp/millisecondsToMinutes.js"}}, "./fp/millisecondsToSeconds": {"require": {"types": "./fp/millisecondsToSeconds.d.cts", "default": "./fp/millisecondsToSeconds.cjs"}, "import": {"types": "./fp/millisecondsToSeconds.d.ts", "default": "./fp/millisecondsToSeconds.js"}}, "./fp/min": {"require": {"types": "./fp/min.d.cts", "default": "./fp/min.cjs"}, "import": {"types": "./fp/min.d.ts", "default": "./fp/min.js"}}, "./fp/minWithOptions": {"require": {"types": "./fp/minWithOptions.d.cts", "default": "./fp/minWithOptions.cjs"}, "import": {"types": "./fp/minWithOptions.d.ts", "default": "./fp/minWithOptions.js"}}, "./fp/minutesToHours": {"require": {"types": "./fp/minutesToHours.d.cts", "default": "./fp/minutesToHours.cjs"}, "import": {"types": "./fp/minutesToHours.d.ts", "default": "./fp/minutesToHours.js"}}, "./fp/minutesToMilliseconds": {"require": {"types": "./fp/minutesToMilliseconds.d.cts", "default": "./fp/minutesToMilliseconds.cjs"}, "import": {"types": "./fp/minutesToMilliseconds.d.ts", "default": "./fp/minutesToMilliseconds.js"}}, "./fp/minutesToSeconds": {"require": {"types": "./fp/minutesToSeconds.d.cts", "default": "./fp/minutesToSeconds.cjs"}, "import": {"types": "./fp/minutesToSeconds.d.ts", "default": "./fp/minutesToSeconds.js"}}, "./fp/monthsToQuarters": {"require": {"types": "./fp/monthsToQuarters.d.cts", "default": "./fp/monthsToQuarters.cjs"}, "import": {"types": "./fp/monthsToQuarters.d.ts", "default": "./fp/monthsToQuarters.js"}}, "./fp/monthsToYears": {"require": {"types": "./fp/monthsToYears.d.cts", "default": "./fp/monthsToYears.cjs"}, "import": {"types": "./fp/monthsToYears.d.ts", "default": "./fp/monthsToYears.js"}}, "./fp/nextDay": {"require": {"types": "./fp/nextDay.d.cts", "default": "./fp/nextDay.cjs"}, "import": {"types": "./fp/nextDay.d.ts", "default": "./fp/nextDay.js"}}, "./fp/nextDayWithOptions": {"require": {"types": "./fp/nextDayWithOptions.d.cts", "default": "./fp/nextDayWithOptions.cjs"}, "import": {"types": "./fp/nextDayWithOptions.d.ts", "default": "./fp/nextDayWithOptions.js"}}, "./fp/nextFriday": {"require": {"types": "./fp/nextFriday.d.cts", "default": "./fp/nextFriday.cjs"}, "import": {"types": "./fp/nextFriday.d.ts", "default": "./fp/nextFriday.js"}}, "./fp/nextFridayWithOptions": {"require": {"types": "./fp/nextFridayWithOptions.d.cts", "default": "./fp/nextFridayWithOptions.cjs"}, "import": {"types": "./fp/nextFridayWithOptions.d.ts", "default": "./fp/nextFridayWithOptions.js"}}, "./fp/nextMonday": {"require": {"types": "./fp/nextMonday.d.cts", "default": "./fp/nextMonday.cjs"}, "import": {"types": "./fp/nextMonday.d.ts", "default": "./fp/nextMonday.js"}}, "./fp/nextMondayWithOptions": {"require": {"types": "./fp/nextMondayWithOptions.d.cts", "default": "./fp/nextMondayWithOptions.cjs"}, "import": {"types": "./fp/nextMondayWithOptions.d.ts", "default": "./fp/nextMondayWithOptions.js"}}, "./fp/nextSaturday": {"require": {"types": "./fp/nextSaturday.d.cts", "default": "./fp/nextSaturday.cjs"}, "import": {"types": "./fp/nextSaturday.d.ts", "default": "./fp/nextSaturday.js"}}, "./fp/nextSaturdayWithOptions": {"require": {"types": "./fp/nextSaturdayWithOptions.d.cts", "default": "./fp/nextSaturdayWithOptions.cjs"}, "import": {"types": "./fp/nextSaturdayWithOptions.d.ts", "default": "./fp/nextSaturdayWithOptions.js"}}, "./fp/nextSunday": {"require": {"types": "./fp/nextSunday.d.cts", "default": "./fp/nextSunday.cjs"}, "import": {"types": "./fp/nextSunday.d.ts", "default": "./fp/nextSunday.js"}}, "./fp/nextSundayWithOptions": {"require": {"types": "./fp/nextSundayWithOptions.d.cts", "default": "./fp/nextSundayWithOptions.cjs"}, "import": {"types": "./fp/nextSundayWithOptions.d.ts", "default": "./fp/nextSundayWithOptions.js"}}, "./fp/nextThursday": {"require": {"types": "./fp/nextThursday.d.cts", "default": "./fp/nextThursday.cjs"}, "import": {"types": "./fp/nextThursday.d.ts", "default": "./fp/nextThursday.js"}}, "./fp/nextThursdayWithOptions": {"require": {"types": "./fp/nextThursdayWithOptions.d.cts", "default": "./fp/nextThursdayWithOptions.cjs"}, "import": {"types": "./fp/nextThursdayWithOptions.d.ts", "default": "./fp/nextThursdayWithOptions.js"}}, "./fp/nextTuesday": {"require": {"types": "./fp/nextTuesday.d.cts", "default": "./fp/nextTuesday.cjs"}, "import": {"types": "./fp/nextTuesday.d.ts", "default": "./fp/nextTuesday.js"}}, "./fp/nextTuesdayWithOptions": {"require": {"types": "./fp/nextTuesdayWithOptions.d.cts", "default": "./fp/nextTuesdayWithOptions.cjs"}, "import": {"types": "./fp/nextTuesdayWithOptions.d.ts", "default": "./fp/nextTuesdayWithOptions.js"}}, "./fp/nextWednesday": {"require": {"types": "./fp/nextWednesday.d.cts", "default": "./fp/nextWednesday.cjs"}, "import": {"types": "./fp/nextWednesday.d.ts", "default": "./fp/nextWednesday.js"}}, "./fp/nextWednesdayWithOptions": {"require": {"types": "./fp/nextWednesdayWithOptions.d.cts", "default": "./fp/nextWednesdayWithOptions.cjs"}, "import": {"types": "./fp/nextWednesdayWithOptions.d.ts", "default": "./fp/nextWednesdayWithOptions.js"}}, "./fp/parse": {"require": {"types": "./fp/parse.d.cts", "default": "./fp/parse.cjs"}, "import": {"types": "./fp/parse.d.ts", "default": "./fp/parse.js"}}, "./fp/parseISO": {"require": {"types": "./fp/parseISO.d.cts", "default": "./fp/parseISO.cjs"}, "import": {"types": "./fp/parseISO.d.ts", "default": "./fp/parseISO.js"}}, "./fp/parseISOWithOptions": {"require": {"types": "./fp/parseISOWithOptions.d.cts", "default": "./fp/parseISOWithOptions.cjs"}, "import": {"types": "./fp/parseISOWithOptions.d.ts", "default": "./fp/parseISOWithOptions.js"}}, "./fp/parseJSON": {"require": {"types": "./fp/parseJSON.d.cts", "default": "./fp/parseJSON.cjs"}, "import": {"types": "./fp/parseJSON.d.ts", "default": "./fp/parseJSON.js"}}, "./fp/parseJSONWithOptions": {"require": {"types": "./fp/parseJSONWithOptions.d.cts", "default": "./fp/parseJSONWithOptions.cjs"}, "import": {"types": "./fp/parseJSONWithOptions.d.ts", "default": "./fp/parseJSONWithOptions.js"}}, "./fp/parseWithOptions": {"require": {"types": "./fp/parseWithOptions.d.cts", "default": "./fp/parseWithOptions.cjs"}, "import": {"types": "./fp/parseWithOptions.d.ts", "default": "./fp/parseWithOptions.js"}}, "./fp/previousDay": {"require": {"types": "./fp/previousDay.d.cts", "default": "./fp/previousDay.cjs"}, "import": {"types": "./fp/previousDay.d.ts", "default": "./fp/previousDay.js"}}, "./fp/previousDayWithOptions": {"require": {"types": "./fp/previousDayWithOptions.d.cts", "default": "./fp/previousDayWithOptions.cjs"}, "import": {"types": "./fp/previousDayWithOptions.d.ts", "default": "./fp/previousDayWithOptions.js"}}, "./fp/previousFriday": {"require": {"types": "./fp/previousFriday.d.cts", "default": "./fp/previousFriday.cjs"}, "import": {"types": "./fp/previousFriday.d.ts", "default": "./fp/previousFriday.js"}}, "./fp/previousFridayWithOptions": {"require": {"types": "./fp/previousFridayWithOptions.d.cts", "default": "./fp/previousFridayWithOptions.cjs"}, "import": {"types": "./fp/previousFridayWithOptions.d.ts", "default": "./fp/previousFridayWithOptions.js"}}, "./fp/previousMonday": {"require": {"types": "./fp/previousMonday.d.cts", "default": "./fp/previousMonday.cjs"}, "import": {"types": "./fp/previousMonday.d.ts", "default": "./fp/previousMonday.js"}}, "./fp/previousMondayWithOptions": {"require": {"types": "./fp/previousMondayWithOptions.d.cts", "default": "./fp/previousMondayWithOptions.cjs"}, "import": {"types": "./fp/previousMondayWithOptions.d.ts", "default": "./fp/previousMondayWithOptions.js"}}, "./fp/previousSaturday": {"require": {"types": "./fp/previousSaturday.d.cts", "default": "./fp/previousSaturday.cjs"}, "import": {"types": "./fp/previousSaturday.d.ts", "default": "./fp/previousSaturday.js"}}, "./fp/previousSaturdayWithOptions": {"require": {"types": "./fp/previousSaturdayWithOptions.d.cts", "default": "./fp/previousSaturdayWithOptions.cjs"}, "import": {"types": "./fp/previousSaturdayWithOptions.d.ts", "default": "./fp/previousSaturdayWithOptions.js"}}, "./fp/previousSunday": {"require": {"types": "./fp/previousSunday.d.cts", "default": "./fp/previousSunday.cjs"}, "import": {"types": "./fp/previousSunday.d.ts", "default": "./fp/previousSunday.js"}}, "./fp/previousSundayWithOptions": {"require": {"types": "./fp/previousSundayWithOptions.d.cts", "default": "./fp/previousSundayWithOptions.cjs"}, "import": {"types": "./fp/previousSundayWithOptions.d.ts", "default": "./fp/previousSundayWithOptions.js"}}, "./fp/previousThursday": {"require": {"types": "./fp/previousThursday.d.cts", "default": "./fp/previousThursday.cjs"}, "import": {"types": "./fp/previousThursday.d.ts", "default": "./fp/previousThursday.js"}}, "./fp/previousThursdayWithOptions": {"require": {"types": "./fp/previousThursdayWithOptions.d.cts", "default": "./fp/previousThursdayWithOptions.cjs"}, "import": {"types": "./fp/previousThursdayWithOptions.d.ts", "default": "./fp/previousThursdayWithOptions.js"}}, "./fp/previousTuesday": {"require": {"types": "./fp/previousTuesday.d.cts", "default": "./fp/previousTuesday.cjs"}, "import": {"types": "./fp/previousTuesday.d.ts", "default": "./fp/previousTuesday.js"}}, "./fp/previousTuesdayWithOptions": {"require": {"types": "./fp/previousTuesdayWithOptions.d.cts", "default": "./fp/previousTuesdayWithOptions.cjs"}, "import": {"types": "./fp/previousTuesdayWithOptions.d.ts", "default": "./fp/previousTuesdayWithOptions.js"}}, "./fp/previousWednesday": {"require": {"types": "./fp/previousWednesday.d.cts", "default": "./fp/previousWednesday.cjs"}, "import": {"types": "./fp/previousWednesday.d.ts", "default": "./fp/previousWednesday.js"}}, "./fp/previousWednesdayWithOptions": {"require": {"types": "./fp/previousWednesdayWithOptions.d.cts", "default": "./fp/previousWednesdayWithOptions.cjs"}, "import": {"types": "./fp/previousWednesdayWithOptions.d.ts", "default": "./fp/previousWednesdayWithOptions.js"}}, "./fp/quartersToMonths": {"require": {"types": "./fp/quartersToMonths.d.cts", "default": "./fp/quartersToMonths.cjs"}, "import": {"types": "./fp/quartersToMonths.d.ts", "default": "./fp/quartersToMonths.js"}}, "./fp/quartersToYears": {"require": {"types": "./fp/quartersToYears.d.cts", "default": "./fp/quartersToYears.cjs"}, "import": {"types": "./fp/quartersToYears.d.ts", "default": "./fp/quartersToYears.js"}}, "./fp/roundToNearestHours": {"require": {"types": "./fp/roundToNearestHours.d.cts", "default": "./fp/roundToNearestHours.cjs"}, "import": {"types": "./fp/roundToNearestHours.d.ts", "default": "./fp/roundToNearestHours.js"}}, "./fp/roundToNearestHoursWithOptions": {"require": {"types": "./fp/roundToNearestHoursWithOptions.d.cts", "default": "./fp/roundToNearestHoursWithOptions.cjs"}, "import": {"types": "./fp/roundToNearestHoursWithOptions.d.ts", "default": "./fp/roundToNearestHoursWithOptions.js"}}, "./fp/roundToNearestMinutes": {"require": {"types": "./fp/roundToNearestMinutes.d.cts", "default": "./fp/roundToNearestMinutes.cjs"}, "import": {"types": "./fp/roundToNearestMinutes.d.ts", "default": "./fp/roundToNearestMinutes.js"}}, "./fp/roundToNearestMinutesWithOptions": {"require": {"types": "./fp/roundToNearestMinutesWithOptions.d.cts", "default": "./fp/roundToNearestMinutesWithOptions.cjs"}, "import": {"types": "./fp/roundToNearestMinutesWithOptions.d.ts", "default": "./fp/roundToNearestMinutesWithOptions.js"}}, "./fp/secondsToHours": {"require": {"types": "./fp/secondsToHours.d.cts", "default": "./fp/secondsToHours.cjs"}, "import": {"types": "./fp/secondsToHours.d.ts", "default": "./fp/secondsToHours.js"}}, "./fp/secondsToMilliseconds": {"require": {"types": "./fp/secondsToMilliseconds.d.cts", "default": "./fp/secondsToMilliseconds.cjs"}, "import": {"types": "./fp/secondsToMilliseconds.d.ts", "default": "./fp/secondsToMilliseconds.js"}}, "./fp/secondsToMinutes": {"require": {"types": "./fp/secondsToMinutes.d.cts", "default": "./fp/secondsToMinutes.cjs"}, "import": {"types": "./fp/secondsToMinutes.d.ts", "default": "./fp/secondsToMinutes.js"}}, "./fp/set": {"require": {"types": "./fp/set.d.cts", "default": "./fp/set.cjs"}, "import": {"types": "./fp/set.d.ts", "default": "./fp/set.js"}}, "./fp/setDate": {"require": {"types": "./fp/setDate.d.cts", "default": "./fp/setDate.cjs"}, "import": {"types": "./fp/setDate.d.ts", "default": "./fp/setDate.js"}}, "./fp/setDateWithOptions": {"require": {"types": "./fp/setDateWithOptions.d.cts", "default": "./fp/setDateWithOptions.cjs"}, "import": {"types": "./fp/setDateWithOptions.d.ts", "default": "./fp/setDateWithOptions.js"}}, "./fp/setDay": {"require": {"types": "./fp/setDay.d.cts", "default": "./fp/setDay.cjs"}, "import": {"types": "./fp/setDay.d.ts", "default": "./fp/setDay.js"}}, "./fp/setDayOfYear": {"require": {"types": "./fp/setDayOfYear.d.cts", "default": "./fp/setDayOfYear.cjs"}, "import": {"types": "./fp/setDayOfYear.d.ts", "default": "./fp/setDayOfYear.js"}}, "./fp/setDayOfYearWithOptions": {"require": {"types": "./fp/setDayOfYearWithOptions.d.cts", "default": "./fp/setDayOfYearWithOptions.cjs"}, "import": {"types": "./fp/setDayOfYearWithOptions.d.ts", "default": "./fp/setDayOfYearWithOptions.js"}}, "./fp/setDayWithOptions": {"require": {"types": "./fp/setDayWithOptions.d.cts", "default": "./fp/setDayWithOptions.cjs"}, "import": {"types": "./fp/setDayWithOptions.d.ts", "default": "./fp/setDayWithOptions.js"}}, "./fp/setHours": {"require": {"types": "./fp/setHours.d.cts", "default": "./fp/setHours.cjs"}, "import": {"types": "./fp/setHours.d.ts", "default": "./fp/setHours.js"}}, "./fp/setHoursWithOptions": {"require": {"types": "./fp/setHoursWithOptions.d.cts", "default": "./fp/setHoursWithOptions.cjs"}, "import": {"types": "./fp/setHoursWithOptions.d.ts", "default": "./fp/setHoursWithOptions.js"}}, "./fp/setISODay": {"require": {"types": "./fp/setISODay.d.cts", "default": "./fp/setISODay.cjs"}, "import": {"types": "./fp/setISODay.d.ts", "default": "./fp/setISODay.js"}}, "./fp/setISODayWithOptions": {"require": {"types": "./fp/setISODayWithOptions.d.cts", "default": "./fp/setISODayWithOptions.cjs"}, "import": {"types": "./fp/setISODayWithOptions.d.ts", "default": "./fp/setISODayWithOptions.js"}}, "./fp/setISOWeek": {"require": {"types": "./fp/setISOWeek.d.cts", "default": "./fp/setISOWeek.cjs"}, "import": {"types": "./fp/setISOWeek.d.ts", "default": "./fp/setISOWeek.js"}}, "./fp/setISOWeekWithOptions": {"require": {"types": "./fp/setISOWeekWithOptions.d.cts", "default": "./fp/setISOWeekWithOptions.cjs"}, "import": {"types": "./fp/setISOWeekWithOptions.d.ts", "default": "./fp/setISOWeekWithOptions.js"}}, "./fp/setISOWeekYear": {"require": {"types": "./fp/setISOWeekYear.d.cts", "default": "./fp/setISOWeekYear.cjs"}, "import": {"types": "./fp/setISOWeekYear.d.ts", "default": "./fp/setISOWeekYear.js"}}, "./fp/setISOWeekYearWithOptions": {"require": {"types": "./fp/setISOWeekYearWithOptions.d.cts", "default": "./fp/setISOWeekYearWithOptions.cjs"}, "import": {"types": "./fp/setISOWeekYearWithOptions.d.ts", "default": "./fp/setISOWeekYearWithOptions.js"}}, "./fp/setMilliseconds": {"require": {"types": "./fp/setMilliseconds.d.cts", "default": "./fp/setMilliseconds.cjs"}, "import": {"types": "./fp/setMilliseconds.d.ts", "default": "./fp/setMilliseconds.js"}}, "./fp/setMillisecondsWithOptions": {"require": {"types": "./fp/setMillisecondsWithOptions.d.cts", "default": "./fp/setMillisecondsWithOptions.cjs"}, "import": {"types": "./fp/setMillisecondsWithOptions.d.ts", "default": "./fp/setMillisecondsWithOptions.js"}}, "./fp/setMinutes": {"require": {"types": "./fp/setMinutes.d.cts", "default": "./fp/setMinutes.cjs"}, "import": {"types": "./fp/setMinutes.d.ts", "default": "./fp/setMinutes.js"}}, "./fp/setMinutesWithOptions": {"require": {"types": "./fp/setMinutesWithOptions.d.cts", "default": "./fp/setMinutesWithOptions.cjs"}, "import": {"types": "./fp/setMinutesWithOptions.d.ts", "default": "./fp/setMinutesWithOptions.js"}}, "./fp/setMonth": {"require": {"types": "./fp/setMonth.d.cts", "default": "./fp/setMonth.cjs"}, "import": {"types": "./fp/setMonth.d.ts", "default": "./fp/setMonth.js"}}, "./fp/setMonthWithOptions": {"require": {"types": "./fp/setMonthWithOptions.d.cts", "default": "./fp/setMonthWithOptions.cjs"}, "import": {"types": "./fp/setMonthWithOptions.d.ts", "default": "./fp/setMonthWithOptions.js"}}, "./fp/setQuarter": {"require": {"types": "./fp/setQuarter.d.cts", "default": "./fp/setQuarter.cjs"}, "import": {"types": "./fp/setQuarter.d.ts", "default": "./fp/setQuarter.js"}}, "./fp/setQuarterWithOptions": {"require": {"types": "./fp/setQuarterWithOptions.d.cts", "default": "./fp/setQuarterWithOptions.cjs"}, "import": {"types": "./fp/setQuarterWithOptions.d.ts", "default": "./fp/setQuarterWithOptions.js"}}, "./fp/setSeconds": {"require": {"types": "./fp/setSeconds.d.cts", "default": "./fp/setSeconds.cjs"}, "import": {"types": "./fp/setSeconds.d.ts", "default": "./fp/setSeconds.js"}}, "./fp/setSecondsWithOptions": {"require": {"types": "./fp/setSecondsWithOptions.d.cts", "default": "./fp/setSecondsWithOptions.cjs"}, "import": {"types": "./fp/setSecondsWithOptions.d.ts", "default": "./fp/setSecondsWithOptions.js"}}, "./fp/setWeek": {"require": {"types": "./fp/setWeek.d.cts", "default": "./fp/setWeek.cjs"}, "import": {"types": "./fp/setWeek.d.ts", "default": "./fp/setWeek.js"}}, "./fp/setWeekWithOptions": {"require": {"types": "./fp/setWeekWithOptions.d.cts", "default": "./fp/setWeekWithOptions.cjs"}, "import": {"types": "./fp/setWeekWithOptions.d.ts", "default": "./fp/setWeekWithOptions.js"}}, "./fp/setWeekYear": {"require": {"types": "./fp/setWeekYear.d.cts", "default": "./fp/setWeekYear.cjs"}, "import": {"types": "./fp/setWeekYear.d.ts", "default": "./fp/setWeekYear.js"}}, "./fp/setWeekYearWithOptions": {"require": {"types": "./fp/setWeekYearWithOptions.d.cts", "default": "./fp/setWeekYearWithOptions.cjs"}, "import": {"types": "./fp/setWeekYearWithOptions.d.ts", "default": "./fp/setWeekYearWithOptions.js"}}, "./fp/setWithOptions": {"require": {"types": "./fp/setWithOptions.d.cts", "default": "./fp/setWithOptions.cjs"}, "import": {"types": "./fp/setWithOptions.d.ts", "default": "./fp/setWithOptions.js"}}, "./fp/setYear": {"require": {"types": "./fp/setYear.d.cts", "default": "./fp/setYear.cjs"}, "import": {"types": "./fp/setYear.d.ts", "default": "./fp/setYear.js"}}, "./fp/setYearWithOptions": {"require": {"types": "./fp/setYearWithOptions.d.cts", "default": "./fp/setYearWithOptions.cjs"}, "import": {"types": "./fp/setYearWithOptions.d.ts", "default": "./fp/setYearWithOptions.js"}}, "./fp/startOfDay": {"require": {"types": "./fp/startOfDay.d.cts", "default": "./fp/startOfDay.cjs"}, "import": {"types": "./fp/startOfDay.d.ts", "default": "./fp/startOfDay.js"}}, "./fp/startOfDayWithOptions": {"require": {"types": "./fp/startOfDayWithOptions.d.cts", "default": "./fp/startOfDayWithOptions.cjs"}, "import": {"types": "./fp/startOfDayWithOptions.d.ts", "default": "./fp/startOfDayWithOptions.js"}}, "./fp/startOfDecade": {"require": {"types": "./fp/startOfDecade.d.cts", "default": "./fp/startOfDecade.cjs"}, "import": {"types": "./fp/startOfDecade.d.ts", "default": "./fp/startOfDecade.js"}}, "./fp/startOfDecadeWithOptions": {"require": {"types": "./fp/startOfDecadeWithOptions.d.cts", "default": "./fp/startOfDecadeWithOptions.cjs"}, "import": {"types": "./fp/startOfDecadeWithOptions.d.ts", "default": "./fp/startOfDecadeWithOptions.js"}}, "./fp/startOfHour": {"require": {"types": "./fp/startOfHour.d.cts", "default": "./fp/startOfHour.cjs"}, "import": {"types": "./fp/startOfHour.d.ts", "default": "./fp/startOfHour.js"}}, "./fp/startOfHourWithOptions": {"require": {"types": "./fp/startOfHourWithOptions.d.cts", "default": "./fp/startOfHourWithOptions.cjs"}, "import": {"types": "./fp/startOfHourWithOptions.d.ts", "default": "./fp/startOfHourWithOptions.js"}}, "./fp/startOfISOWeek": {"require": {"types": "./fp/startOfISOWeek.d.cts", "default": "./fp/startOfISOWeek.cjs"}, "import": {"types": "./fp/startOfISOWeek.d.ts", "default": "./fp/startOfISOWeek.js"}}, "./fp/startOfISOWeekWithOptions": {"require": {"types": "./fp/startOfISOWeekWithOptions.d.cts", "default": "./fp/startOfISOWeekWithOptions.cjs"}, "import": {"types": "./fp/startOfISOWeekWithOptions.d.ts", "default": "./fp/startOfISOWeekWithOptions.js"}}, "./fp/startOfISOWeekYear": {"require": {"types": "./fp/startOfISOWeekYear.d.cts", "default": "./fp/startOfISOWeekYear.cjs"}, "import": {"types": "./fp/startOfISOWeekYear.d.ts", "default": "./fp/startOfISOWeekYear.js"}}, "./fp/startOfISOWeekYearWithOptions": {"require": {"types": "./fp/startOfISOWeekYearWithOptions.d.cts", "default": "./fp/startOfISOWeekYearWithOptions.cjs"}, "import": {"types": "./fp/startOfISOWeekYearWithOptions.d.ts", "default": "./fp/startOfISOWeekYearWithOptions.js"}}, "./fp/startOfMinute": {"require": {"types": "./fp/startOfMinute.d.cts", "default": "./fp/startOfMinute.cjs"}, "import": {"types": "./fp/startOfMinute.d.ts", "default": "./fp/startOfMinute.js"}}, "./fp/startOfMinuteWithOptions": {"require": {"types": "./fp/startOfMinuteWithOptions.d.cts", "default": "./fp/startOfMinuteWithOptions.cjs"}, "import": {"types": "./fp/startOfMinuteWithOptions.d.ts", "default": "./fp/startOfMinuteWithOptions.js"}}, "./fp/startOfMonth": {"require": {"types": "./fp/startOfMonth.d.cts", "default": "./fp/startOfMonth.cjs"}, "import": {"types": "./fp/startOfMonth.d.ts", "default": "./fp/startOfMonth.js"}}, "./fp/startOfMonthWithOptions": {"require": {"types": "./fp/startOfMonthWithOptions.d.cts", "default": "./fp/startOfMonthWithOptions.cjs"}, "import": {"types": "./fp/startOfMonthWithOptions.d.ts", "default": "./fp/startOfMonthWithOptions.js"}}, "./fp/startOfQuarter": {"require": {"types": "./fp/startOfQuarter.d.cts", "default": "./fp/startOfQuarter.cjs"}, "import": {"types": "./fp/startOfQuarter.d.ts", "default": "./fp/startOfQuarter.js"}}, "./fp/startOfQuarterWithOptions": {"require": {"types": "./fp/startOfQuarterWithOptions.d.cts", "default": "./fp/startOfQuarterWithOptions.cjs"}, "import": {"types": "./fp/startOfQuarterWithOptions.d.ts", "default": "./fp/startOfQuarterWithOptions.js"}}, "./fp/startOfSecond": {"require": {"types": "./fp/startOfSecond.d.cts", "default": "./fp/startOfSecond.cjs"}, "import": {"types": "./fp/startOfSecond.d.ts", "default": "./fp/startOfSecond.js"}}, "./fp/startOfSecondWithOptions": {"require": {"types": "./fp/startOfSecondWithOptions.d.cts", "default": "./fp/startOfSecondWithOptions.cjs"}, "import": {"types": "./fp/startOfSecondWithOptions.d.ts", "default": "./fp/startOfSecondWithOptions.js"}}, "./fp/startOfWeek": {"require": {"types": "./fp/startOfWeek.d.cts", "default": "./fp/startOfWeek.cjs"}, "import": {"types": "./fp/startOfWeek.d.ts", "default": "./fp/startOfWeek.js"}}, "./fp/startOfWeekWithOptions": {"require": {"types": "./fp/startOfWeekWithOptions.d.cts", "default": "./fp/startOfWeekWithOptions.cjs"}, "import": {"types": "./fp/startOfWeekWithOptions.d.ts", "default": "./fp/startOfWeekWithOptions.js"}}, "./fp/startOfWeekYear": {"require": {"types": "./fp/startOfWeekYear.d.cts", "default": "./fp/startOfWeekYear.cjs"}, "import": {"types": "./fp/startOfWeekYear.d.ts", "default": "./fp/startOfWeekYear.js"}}, "./fp/startOfWeekYearWithOptions": {"require": {"types": "./fp/startOfWeekYearWithOptions.d.cts", "default": "./fp/startOfWeekYearWithOptions.cjs"}, "import": {"types": "./fp/startOfWeekYearWithOptions.d.ts", "default": "./fp/startOfWeekYearWithOptions.js"}}, "./fp/startOfYear": {"require": {"types": "./fp/startOfYear.d.cts", "default": "./fp/startOfYear.cjs"}, "import": {"types": "./fp/startOfYear.d.ts", "default": "./fp/startOfYear.js"}}, "./fp/startOfYearWithOptions": {"require": {"types": "./fp/startOfYearWithOptions.d.cts", "default": "./fp/startOfYearWithOptions.cjs"}, "import": {"types": "./fp/startOfYearWithOptions.d.ts", "default": "./fp/startOfYearWithOptions.js"}}, "./fp/sub": {"require": {"types": "./fp/sub.d.cts", "default": "./fp/sub.cjs"}, "import": {"types": "./fp/sub.d.ts", "default": "./fp/sub.js"}}, "./fp/subBusinessDays": {"require": {"types": "./fp/subBusinessDays.d.cts", "default": "./fp/subBusinessDays.cjs"}, "import": {"types": "./fp/subBusinessDays.d.ts", "default": "./fp/subBusinessDays.js"}}, "./fp/subBusinessDaysWithOptions": {"require": {"types": "./fp/subBusinessDaysWithOptions.d.cts", "default": "./fp/subBusinessDaysWithOptions.cjs"}, "import": {"types": "./fp/subBusinessDaysWithOptions.d.ts", "default": "./fp/subBusinessDaysWithOptions.js"}}, "./fp/subDays": {"require": {"types": "./fp/subDays.d.cts", "default": "./fp/subDays.cjs"}, "import": {"types": "./fp/subDays.d.ts", "default": "./fp/subDays.js"}}, "./fp/subDaysWithOptions": {"require": {"types": "./fp/subDaysWithOptions.d.cts", "default": "./fp/subDaysWithOptions.cjs"}, "import": {"types": "./fp/subDaysWithOptions.d.ts", "default": "./fp/subDaysWithOptions.js"}}, "./fp/subHours": {"require": {"types": "./fp/subHours.d.cts", "default": "./fp/subHours.cjs"}, "import": {"types": "./fp/subHours.d.ts", "default": "./fp/subHours.js"}}, "./fp/subHoursWithOptions": {"require": {"types": "./fp/subHoursWithOptions.d.cts", "default": "./fp/subHoursWithOptions.cjs"}, "import": {"types": "./fp/subHoursWithOptions.d.ts", "default": "./fp/subHoursWithOptions.js"}}, "./fp/subISOWeekYears": {"require": {"types": "./fp/subISOWeekYears.d.cts", "default": "./fp/subISOWeekYears.cjs"}, "import": {"types": "./fp/subISOWeekYears.d.ts", "default": "./fp/subISOWeekYears.js"}}, "./fp/subISOWeekYearsWithOptions": {"require": {"types": "./fp/subISOWeekYearsWithOptions.d.cts", "default": "./fp/subISOWeekYearsWithOptions.cjs"}, "import": {"types": "./fp/subISOWeekYearsWithOptions.d.ts", "default": "./fp/subISOWeekYearsWithOptions.js"}}, "./fp/subMilliseconds": {"require": {"types": "./fp/subMilliseconds.d.cts", "default": "./fp/subMilliseconds.cjs"}, "import": {"types": "./fp/subMilliseconds.d.ts", "default": "./fp/subMilliseconds.js"}}, "./fp/subMillisecondsWithOptions": {"require": {"types": "./fp/subMillisecondsWithOptions.d.cts", "default": "./fp/subMillisecondsWithOptions.cjs"}, "import": {"types": "./fp/subMillisecondsWithOptions.d.ts", "default": "./fp/subMillisecondsWithOptions.js"}}, "./fp/subMinutes": {"require": {"types": "./fp/subMinutes.d.cts", "default": "./fp/subMinutes.cjs"}, "import": {"types": "./fp/subMinutes.d.ts", "default": "./fp/subMinutes.js"}}, "./fp/subMinutesWithOptions": {"require": {"types": "./fp/subMinutesWithOptions.d.cts", "default": "./fp/subMinutesWithOptions.cjs"}, "import": {"types": "./fp/subMinutesWithOptions.d.ts", "default": "./fp/subMinutesWithOptions.js"}}, "./fp/subMonths": {"require": {"types": "./fp/subMonths.d.cts", "default": "./fp/subMonths.cjs"}, "import": {"types": "./fp/subMonths.d.ts", "default": "./fp/subMonths.js"}}, "./fp/subMonthsWithOptions": {"require": {"types": "./fp/subMonthsWithOptions.d.cts", "default": "./fp/subMonthsWithOptions.cjs"}, "import": {"types": "./fp/subMonthsWithOptions.d.ts", "default": "./fp/subMonthsWithOptions.js"}}, "./fp/subQuarters": {"require": {"types": "./fp/subQuarters.d.cts", "default": "./fp/subQuarters.cjs"}, "import": {"types": "./fp/subQuarters.d.ts", "default": "./fp/subQuarters.js"}}, "./fp/subQuartersWithOptions": {"require": {"types": "./fp/subQuartersWithOptions.d.cts", "default": "./fp/subQuartersWithOptions.cjs"}, "import": {"types": "./fp/subQuartersWithOptions.d.ts", "default": "./fp/subQuartersWithOptions.js"}}, "./fp/subSeconds": {"require": {"types": "./fp/subSeconds.d.cts", "default": "./fp/subSeconds.cjs"}, "import": {"types": "./fp/subSeconds.d.ts", "default": "./fp/subSeconds.js"}}, "./fp/subSecondsWithOptions": {"require": {"types": "./fp/subSecondsWithOptions.d.cts", "default": "./fp/subSecondsWithOptions.cjs"}, "import": {"types": "./fp/subSecondsWithOptions.d.ts", "default": "./fp/subSecondsWithOptions.js"}}, "./fp/subWeeks": {"require": {"types": "./fp/subWeeks.d.cts", "default": "./fp/subWeeks.cjs"}, "import": {"types": "./fp/subWeeks.d.ts", "default": "./fp/subWeeks.js"}}, "./fp/subWeeksWithOptions": {"require": {"types": "./fp/subWeeksWithOptions.d.cts", "default": "./fp/subWeeksWithOptions.cjs"}, "import": {"types": "./fp/subWeeksWithOptions.d.ts", "default": "./fp/subWeeksWithOptions.js"}}, "./fp/subWithOptions": {"require": {"types": "./fp/subWithOptions.d.cts", "default": "./fp/subWithOptions.cjs"}, "import": {"types": "./fp/subWithOptions.d.ts", "default": "./fp/subWithOptions.js"}}, "./fp/subYears": {"require": {"types": "./fp/subYears.d.cts", "default": "./fp/subYears.cjs"}, "import": {"types": "./fp/subYears.d.ts", "default": "./fp/subYears.js"}}, "./fp/subYearsWithOptions": {"require": {"types": "./fp/subYearsWithOptions.d.cts", "default": "./fp/subYearsWithOptions.cjs"}, "import": {"types": "./fp/subYearsWithOptions.d.ts", "default": "./fp/subYearsWithOptions.js"}}, "./fp/toDate": {"require": {"types": "./fp/toDate.d.cts", "default": "./fp/toDate.cjs"}, "import": {"types": "./fp/toDate.d.ts", "default": "./fp/toDate.js"}}, "./fp/transpose": {"require": {"types": "./fp/transpose.d.cts", "default": "./fp/transpose.cjs"}, "import": {"types": "./fp/transpose.d.ts", "default": "./fp/transpose.js"}}, "./fp/weeksToDays": {"require": {"types": "./fp/weeksToDays.d.cts", "default": "./fp/weeksToDays.cjs"}, "import": {"types": "./fp/weeksToDays.d.ts", "default": "./fp/weeksToDays.js"}}, "./fp/yearsToDays": {"require": {"types": "./fp/yearsToDays.d.cts", "default": "./fp/yearsToDays.cjs"}, "import": {"types": "./fp/yearsToDays.d.ts", "default": "./fp/yearsToDays.js"}}, "./fp/yearsToMonths": {"require": {"types": "./fp/yearsToMonths.d.cts", "default": "./fp/yearsToMonths.cjs"}, "import": {"types": "./fp/yearsToMonths.d.ts", "default": "./fp/yearsToMonths.js"}}, "./fp/yearsToQuarters": {"require": {"types": "./fp/yearsToQuarters.d.cts", "default": "./fp/yearsToQuarters.cjs"}, "import": {"types": "./fp/yearsToQuarters.d.ts", "default": "./fp/yearsToQuarters.js"}}, "./locale/af": {"require": {"types": "./locale/af.d.cts", "default": "./locale/af.cjs"}, "import": {"types": "./locale/af.d.ts", "default": "./locale/af.js"}}, "./locale/ar": {"require": {"types": "./locale/ar.d.cts", "default": "./locale/ar.cjs"}, "import": {"types": "./locale/ar.d.ts", "default": "./locale/ar.js"}}, "./locale/ar-DZ": {"require": {"types": "./locale/ar-DZ.d.cts", "default": "./locale/ar-DZ.cjs"}, "import": {"types": "./locale/ar-DZ.d.ts", "default": "./locale/ar-DZ.js"}}, "./locale/ar-EG": {"require": {"types": "./locale/ar-EG.d.cts", "default": "./locale/ar-EG.cjs"}, "import": {"types": "./locale/ar-EG.d.ts", "default": "./locale/ar-EG.js"}}, "./locale/ar-MA": {"require": {"types": "./locale/ar-MA.d.cts", "default": "./locale/ar-MA.cjs"}, "import": {"types": "./locale/ar-MA.d.ts", "default": "./locale/ar-MA.js"}}, "./locale/ar-SA": {"require": {"types": "./locale/ar-SA.d.cts", "default": "./locale/ar-SA.cjs"}, "import": {"types": "./locale/ar-SA.d.ts", "default": "./locale/ar-SA.js"}}, "./locale/ar-TN": {"require": {"types": "./locale/ar-TN.d.cts", "default": "./locale/ar-TN.cjs"}, "import": {"types": "./locale/ar-TN.d.ts", "default": "./locale/ar-TN.js"}}, "./locale/az": {"require": {"types": "./locale/az.d.cts", "default": "./locale/az.cjs"}, "import": {"types": "./locale/az.d.ts", "default": "./locale/az.js"}}, "./locale/be": {"require": {"types": "./locale/be.d.cts", "default": "./locale/be.cjs"}, "import": {"types": "./locale/be.d.ts", "default": "./locale/be.js"}}, "./locale/be-tarask": {"require": {"types": "./locale/be-tarask.d.cts", "default": "./locale/be-tarask.cjs"}, "import": {"types": "./locale/be-tarask.d.ts", "default": "./locale/be-tarask.js"}}, "./locale/bg": {"require": {"types": "./locale/bg.d.cts", "default": "./locale/bg.cjs"}, "import": {"types": "./locale/bg.d.ts", "default": "./locale/bg.js"}}, "./locale/bn": {"require": {"types": "./locale/bn.d.cts", "default": "./locale/bn.cjs"}, "import": {"types": "./locale/bn.d.ts", "default": "./locale/bn.js"}}, "./locale/bs": {"require": {"types": "./locale/bs.d.cts", "default": "./locale/bs.cjs"}, "import": {"types": "./locale/bs.d.ts", "default": "./locale/bs.js"}}, "./locale/ca": {"require": {"types": "./locale/ca.d.cts", "default": "./locale/ca.cjs"}, "import": {"types": "./locale/ca.d.ts", "default": "./locale/ca.js"}}, "./locale/ckb": {"require": {"types": "./locale/ckb.d.cts", "default": "./locale/ckb.cjs"}, "import": {"types": "./locale/ckb.d.ts", "default": "./locale/ckb.js"}}, "./locale/cs": {"require": {"types": "./locale/cs.d.cts", "default": "./locale/cs.cjs"}, "import": {"types": "./locale/cs.d.ts", "default": "./locale/cs.js"}}, "./locale/cy": {"require": {"types": "./locale/cy.d.cts", "default": "./locale/cy.cjs"}, "import": {"types": "./locale/cy.d.ts", "default": "./locale/cy.js"}}, "./locale/da": {"require": {"types": "./locale/da.d.cts", "default": "./locale/da.cjs"}, "import": {"types": "./locale/da.d.ts", "default": "./locale/da.js"}}, "./locale/de": {"require": {"types": "./locale/de.d.cts", "default": "./locale/de.cjs"}, "import": {"types": "./locale/de.d.ts", "default": "./locale/de.js"}}, "./locale/de-AT": {"require": {"types": "./locale/de-AT.d.cts", "default": "./locale/de-AT.cjs"}, "import": {"types": "./locale/de-AT.d.ts", "default": "./locale/de-AT.js"}}, "./locale/el": {"require": {"types": "./locale/el.d.cts", "default": "./locale/el.cjs"}, "import": {"types": "./locale/el.d.ts", "default": "./locale/el.js"}}, "./locale/en-AU": {"require": {"types": "./locale/en-AU.d.cts", "default": "./locale/en-AU.cjs"}, "import": {"types": "./locale/en-AU.d.ts", "default": "./locale/en-AU.js"}}, "./locale/en-CA": {"require": {"types": "./locale/en-CA.d.cts", "default": "./locale/en-CA.cjs"}, "import": {"types": "./locale/en-CA.d.ts", "default": "./locale/en-CA.js"}}, "./locale/en-GB": {"require": {"types": "./locale/en-GB.d.cts", "default": "./locale/en-GB.cjs"}, "import": {"types": "./locale/en-GB.d.ts", "default": "./locale/en-GB.js"}}, "./locale/en-IE": {"require": {"types": "./locale/en-IE.d.cts", "default": "./locale/en-IE.cjs"}, "import": {"types": "./locale/en-IE.d.ts", "default": "./locale/en-IE.js"}}, "./locale/en-IN": {"require": {"types": "./locale/en-IN.d.cts", "default": "./locale/en-IN.cjs"}, "import": {"types": "./locale/en-IN.d.ts", "default": "./locale/en-IN.js"}}, "./locale/en-NZ": {"require": {"types": "./locale/en-NZ.d.cts", "default": "./locale/en-NZ.cjs"}, "import": {"types": "./locale/en-NZ.d.ts", "default": "./locale/en-NZ.js"}}, "./locale/en-US": {"require": {"types": "./locale/en-US.d.cts", "default": "./locale/en-US.cjs"}, "import": {"types": "./locale/en-US.d.ts", "default": "./locale/en-US.js"}}, "./locale/en-ZA": {"require": {"types": "./locale/en-ZA.d.cts", "default": "./locale/en-ZA.cjs"}, "import": {"types": "./locale/en-ZA.d.ts", "default": "./locale/en-ZA.js"}}, "./locale/eo": {"require": {"types": "./locale/eo.d.cts", "default": "./locale/eo.cjs"}, "import": {"types": "./locale/eo.d.ts", "default": "./locale/eo.js"}}, "./locale/es": {"require": {"types": "./locale/es.d.cts", "default": "./locale/es.cjs"}, "import": {"types": "./locale/es.d.ts", "default": "./locale/es.js"}}, "./locale/et": {"require": {"types": "./locale/et.d.cts", "default": "./locale/et.cjs"}, "import": {"types": "./locale/et.d.ts", "default": "./locale/et.js"}}, "./locale/eu": {"require": {"types": "./locale/eu.d.cts", "default": "./locale/eu.cjs"}, "import": {"types": "./locale/eu.d.ts", "default": "./locale/eu.js"}}, "./locale/fa-IR": {"require": {"types": "./locale/fa-IR.d.cts", "default": "./locale/fa-IR.cjs"}, "import": {"types": "./locale/fa-IR.d.ts", "default": "./locale/fa-IR.js"}}, "./locale/fi": {"require": {"types": "./locale/fi.d.cts", "default": "./locale/fi.cjs"}, "import": {"types": "./locale/fi.d.ts", "default": "./locale/fi.js"}}, "./locale/fr": {"require": {"types": "./locale/fr.d.cts", "default": "./locale/fr.cjs"}, "import": {"types": "./locale/fr.d.ts", "default": "./locale/fr.js"}}, "./locale/fr-CA": {"require": {"types": "./locale/fr-CA.d.cts", "default": "./locale/fr-CA.cjs"}, "import": {"types": "./locale/fr-CA.d.ts", "default": "./locale/fr-CA.js"}}, "./locale/fr-CH": {"require": {"types": "./locale/fr-CH.d.cts", "default": "./locale/fr-CH.cjs"}, "import": {"types": "./locale/fr-CH.d.ts", "default": "./locale/fr-CH.js"}}, "./locale/fy": {"require": {"types": "./locale/fy.d.cts", "default": "./locale/fy.cjs"}, "import": {"types": "./locale/fy.d.ts", "default": "./locale/fy.js"}}, "./locale/gd": {"require": {"types": "./locale/gd.d.cts", "default": "./locale/gd.cjs"}, "import": {"types": "./locale/gd.d.ts", "default": "./locale/gd.js"}}, "./locale/gl": {"require": {"types": "./locale/gl.d.cts", "default": "./locale/gl.cjs"}, "import": {"types": "./locale/gl.d.ts", "default": "./locale/gl.js"}}, "./locale/gu": {"require": {"types": "./locale/gu.d.cts", "default": "./locale/gu.cjs"}, "import": {"types": "./locale/gu.d.ts", "default": "./locale/gu.js"}}, "./locale/he": {"require": {"types": "./locale/he.d.cts", "default": "./locale/he.cjs"}, "import": {"types": "./locale/he.d.ts", "default": "./locale/he.js"}}, "./locale/hi": {"require": {"types": "./locale/hi.d.cts", "default": "./locale/hi.cjs"}, "import": {"types": "./locale/hi.d.ts", "default": "./locale/hi.js"}}, "./locale/hr": {"require": {"types": "./locale/hr.d.cts", "default": "./locale/hr.cjs"}, "import": {"types": "./locale/hr.d.ts", "default": "./locale/hr.js"}}, "./locale/ht": {"require": {"types": "./locale/ht.d.cts", "default": "./locale/ht.cjs"}, "import": {"types": "./locale/ht.d.ts", "default": "./locale/ht.js"}}, "./locale/hu": {"require": {"types": "./locale/hu.d.cts", "default": "./locale/hu.cjs"}, "import": {"types": "./locale/hu.d.ts", "default": "./locale/hu.js"}}, "./locale/hy": {"require": {"types": "./locale/hy.d.cts", "default": "./locale/hy.cjs"}, "import": {"types": "./locale/hy.d.ts", "default": "./locale/hy.js"}}, "./locale/id": {"require": {"types": "./locale/id.d.cts", "default": "./locale/id.cjs"}, "import": {"types": "./locale/id.d.ts", "default": "./locale/id.js"}}, "./locale/is": {"require": {"types": "./locale/is.d.cts", "default": "./locale/is.cjs"}, "import": {"types": "./locale/is.d.ts", "default": "./locale/is.js"}}, "./locale/it": {"require": {"types": "./locale/it.d.cts", "default": "./locale/it.cjs"}, "import": {"types": "./locale/it.d.ts", "default": "./locale/it.js"}}, "./locale/it-CH": {"require": {"types": "./locale/it-CH.d.cts", "default": "./locale/it-CH.cjs"}, "import": {"types": "./locale/it-CH.d.ts", "default": "./locale/it-CH.js"}}, "./locale/ja": {"require": {"types": "./locale/ja.d.cts", "default": "./locale/ja.cjs"}, "import": {"types": "./locale/ja.d.ts", "default": "./locale/ja.js"}}, "./locale/ja-Hira": {"require": {"types": "./locale/ja-Hira.d.cts", "default": "./locale/ja-Hira.cjs"}, "import": {"types": "./locale/ja-Hira.d.ts", "default": "./locale/ja-Hira.js"}}, "./locale/ka": {"require": {"types": "./locale/ka.d.cts", "default": "./locale/ka.cjs"}, "import": {"types": "./locale/ka.d.ts", "default": "./locale/ka.js"}}, "./locale/kk": {"require": {"types": "./locale/kk.d.cts", "default": "./locale/kk.cjs"}, "import": {"types": "./locale/kk.d.ts", "default": "./locale/kk.js"}}, "./locale/km": {"require": {"types": "./locale/km.d.cts", "default": "./locale/km.cjs"}, "import": {"types": "./locale/km.d.ts", "default": "./locale/km.js"}}, "./locale/kn": {"require": {"types": "./locale/kn.d.cts", "default": "./locale/kn.cjs"}, "import": {"types": "./locale/kn.d.ts", "default": "./locale/kn.js"}}, "./locale/ko": {"require": {"types": "./locale/ko.d.cts", "default": "./locale/ko.cjs"}, "import": {"types": "./locale/ko.d.ts", "default": "./locale/ko.js"}}, "./locale/lb": {"require": {"types": "./locale/lb.d.cts", "default": "./locale/lb.cjs"}, "import": {"types": "./locale/lb.d.ts", "default": "./locale/lb.js"}}, "./locale/lt": {"require": {"types": "./locale/lt.d.cts", "default": "./locale/lt.cjs"}, "import": {"types": "./locale/lt.d.ts", "default": "./locale/lt.js"}}, "./locale/lv": {"require": {"types": "./locale/lv.d.cts", "default": "./locale/lv.cjs"}, "import": {"types": "./locale/lv.d.ts", "default": "./locale/lv.js"}}, "./locale/mk": {"require": {"types": "./locale/mk.d.cts", "default": "./locale/mk.cjs"}, "import": {"types": "./locale/mk.d.ts", "default": "./locale/mk.js"}}, "./locale/mn": {"require": {"types": "./locale/mn.d.cts", "default": "./locale/mn.cjs"}, "import": {"types": "./locale/mn.d.ts", "default": "./locale/mn.js"}}, "./locale/ms": {"require": {"types": "./locale/ms.d.cts", "default": "./locale/ms.cjs"}, "import": {"types": "./locale/ms.d.ts", "default": "./locale/ms.js"}}, "./locale/mt": {"require": {"types": "./locale/mt.d.cts", "default": "./locale/mt.cjs"}, "import": {"types": "./locale/mt.d.ts", "default": "./locale/mt.js"}}, "./locale/nb": {"require": {"types": "./locale/nb.d.cts", "default": "./locale/nb.cjs"}, "import": {"types": "./locale/nb.d.ts", "default": "./locale/nb.js"}}, "./locale/nl": {"require": {"types": "./locale/nl.d.cts", "default": "./locale/nl.cjs"}, "import": {"types": "./locale/nl.d.ts", "default": "./locale/nl.js"}}, "./locale/nl-BE": {"require": {"types": "./locale/nl-BE.d.cts", "default": "./locale/nl-BE.cjs"}, "import": {"types": "./locale/nl-BE.d.ts", "default": "./locale/nl-BE.js"}}, "./locale/nn": {"require": {"types": "./locale/nn.d.cts", "default": "./locale/nn.cjs"}, "import": {"types": "./locale/nn.d.ts", "default": "./locale/nn.js"}}, "./locale/oc": {"require": {"types": "./locale/oc.d.cts", "default": "./locale/oc.cjs"}, "import": {"types": "./locale/oc.d.ts", "default": "./locale/oc.js"}}, "./locale/pl": {"require": {"types": "./locale/pl.d.cts", "default": "./locale/pl.cjs"}, "import": {"types": "./locale/pl.d.ts", "default": "./locale/pl.js"}}, "./locale/pt": {"require": {"types": "./locale/pt.d.cts", "default": "./locale/pt.cjs"}, "import": {"types": "./locale/pt.d.ts", "default": "./locale/pt.js"}}, "./locale/pt-BR": {"require": {"types": "./locale/pt-BR.d.cts", "default": "./locale/pt-BR.cjs"}, "import": {"types": "./locale/pt-BR.d.ts", "default": "./locale/pt-BR.js"}}, "./locale/ro": {"require": {"types": "./locale/ro.d.cts", "default": "./locale/ro.cjs"}, "import": {"types": "./locale/ro.d.ts", "default": "./locale/ro.js"}}, "./locale/ru": {"require": {"types": "./locale/ru.d.cts", "default": "./locale/ru.cjs"}, "import": {"types": "./locale/ru.d.ts", "default": "./locale/ru.js"}}, "./locale/se": {"require": {"types": "./locale/se.d.cts", "default": "./locale/se.cjs"}, "import": {"types": "./locale/se.d.ts", "default": "./locale/se.js"}}, "./locale/sk": {"require": {"types": "./locale/sk.d.cts", "default": "./locale/sk.cjs"}, "import": {"types": "./locale/sk.d.ts", "default": "./locale/sk.js"}}, "./locale/sl": {"require": {"types": "./locale/sl.d.cts", "default": "./locale/sl.cjs"}, "import": {"types": "./locale/sl.d.ts", "default": "./locale/sl.js"}}, "./locale/sq": {"require": {"types": "./locale/sq.d.cts", "default": "./locale/sq.cjs"}, "import": {"types": "./locale/sq.d.ts", "default": "./locale/sq.js"}}, "./locale/sr": {"require": {"types": "./locale/sr.d.cts", "default": "./locale/sr.cjs"}, "import": {"types": "./locale/sr.d.ts", "default": "./locale/sr.js"}}, "./locale/sr-Latn": {"require": {"types": "./locale/sr-Latn.d.cts", "default": "./locale/sr-Latn.cjs"}, "import": {"types": "./locale/sr-Latn.d.ts", "default": "./locale/sr-Latn.js"}}, "./locale/sv": {"require": {"types": "./locale/sv.d.cts", "default": "./locale/sv.cjs"}, "import": {"types": "./locale/sv.d.ts", "default": "./locale/sv.js"}}, "./locale/ta": {"require": {"types": "./locale/ta.d.cts", "default": "./locale/ta.cjs"}, "import": {"types": "./locale/ta.d.ts", "default": "./locale/ta.js"}}, "./locale/te": {"require": {"types": "./locale/te.d.cts", "default": "./locale/te.cjs"}, "import": {"types": "./locale/te.d.ts", "default": "./locale/te.js"}}, "./locale/th": {"require": {"types": "./locale/th.d.cts", "default": "./locale/th.cjs"}, "import": {"types": "./locale/th.d.ts", "default": "./locale/th.js"}}, "./locale/tr": {"require": {"types": "./locale/tr.d.cts", "default": "./locale/tr.cjs"}, "import": {"types": "./locale/tr.d.ts", "default": "./locale/tr.js"}}, "./locale/ug": {"require": {"types": "./locale/ug.d.cts", "default": "./locale/ug.cjs"}, "import": {"types": "./locale/ug.d.ts", "default": "./locale/ug.js"}}, "./locale/uk": {"require": {"types": "./locale/uk.d.cts", "default": "./locale/uk.cjs"}, "import": {"types": "./locale/uk.d.ts", "default": "./locale/uk.js"}}, "./locale/uz": {"require": {"types": "./locale/uz.d.cts", "default": "./locale/uz.cjs"}, "import": {"types": "./locale/uz.d.ts", "default": "./locale/uz.js"}}, "./locale/uz-Cyrl": {"require": {"types": "./locale/uz-Cyrl.d.cts", "default": "./locale/uz-Cyrl.cjs"}, "import": {"types": "./locale/uz-Cyrl.d.ts", "default": "./locale/uz-Cyrl.js"}}, "./locale/vi": {"require": {"types": "./locale/vi.d.cts", "default": "./locale/vi.cjs"}, "import": {"types": "./locale/vi.d.ts", "default": "./locale/vi.js"}}, "./locale/zh-CN": {"require": {"types": "./locale/zh-CN.d.cts", "default": "./locale/zh-CN.cjs"}, "import": {"types": "./locale/zh-CN.d.ts", "default": "./locale/zh-CN.js"}}, "./locale/zh-HK": {"require": {"types": "./locale/zh-HK.d.cts", "default": "./locale/zh-HK.cjs"}, "import": {"types": "./locale/zh-HK.d.ts", "default": "./locale/zh-HK.js"}}, "./locale/zh-TW": {"require": {"types": "./locale/zh-TW.d.cts", "default": "./locale/zh-TW.cjs"}, "import": {"types": "./locale/zh-TW.d.ts", "default": "./locale/zh-TW.js"}}}, "scripts": {"test": "vitest run", "lint": "eslint .", "types": "tsc --noEmit", "locale-snapshots": "env TZ=utc tsx ./scripts/build/localeSnapshots/index.ts", "stats": "cloc . --exclude-dir=node_modules,lib,examples,tmp,.git --exclude-ext=yaml,yml,json,svg,xml"}, "devDependencies": {"@arethetypeswrong/cli": "^0.16.2", "@babel/cli": "^7.22.10", "@babel/core": "^7.22.10", "@babel/preset-env": "^7.22.10", "@babel/preset-typescript": "^7.22.5", "@date-fns/docs": "^0.38.0", "@date-fns/tz": "^1.0.2", "@date-fns/utc": "^1.2.0", "@octokit/core": "^3.2.5", "@size-limit/esbuild": "^11.0.1", "@size-limit/file": "^11.0.1", "@types/bun": "^1.1.9", "@types/lodash": "^4.14.202", "@types/node": "^20.14.2", "@types/sinon": "^9.0.6", "@typescript-eslint/eslint-plugin": "^8.5.0", "@typescript-eslint/parser": "^8.5.0", "@vitest/browser": "^1.3.1", "@vitest/coverage-v8": "^1.3.1", "babel-plugin-replace-import-extension": "^1.1.4", "bun": "^1.1.27", "cloc": "^2.2.0", "coveralls": "^3.1.1", "eslint": "^9.10.0", "firebase": "^3.7.1", "fp-ts": "^2.16.2", "js-fns": "^2.5.1", "jscodeshift": "^0.15.2", "lodash": "^4.17.21", "playwright": "^1.40.1", "prettier": "^3.1.0", "simple-git": "^2.35.2", "sinon": "^7.4.1", "size-limit": "^11.0.1", "tsx": "^4.6.1", "typedoc": "^0.26.7", "typedoc-plugin-missing-exports": "^3.0.0", "typescript": "^5.4.5", "vitest": "^1.3.1"}}