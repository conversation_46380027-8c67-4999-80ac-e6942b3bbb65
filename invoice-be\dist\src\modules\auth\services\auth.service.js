"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var AuthService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthService = void 0;
const common_1 = require("@nestjs/common");
const jwt_1 = require("@nestjs/jwt");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const config_1 = require("@nestjs/config");
const user_entity_1 = require("../../../database/entities/user.entity");
let AuthService = AuthService_1 = class AuthService {
    userRepository;
    jwtService;
    configService;
    logger = new common_1.Logger(AuthService_1.name);
    constructor(userRepository, jwtService, configService) {
        this.userRepository = userRepository;
        this.jwtService = jwtService;
        this.configService = configService;
    }
    async login(loginDto) {
        const { username, password } = loginDto;
        const user = await this.userRepository.findOne({
            where: [{ username }, { email: username }],
        });
        if (!user) {
            this.logger.warn(`Login attempt with invalid username: ${username}`);
            throw new common_1.UnauthorizedException('Invalid credentials');
        }
        if (!user.isActive) {
            this.logger.warn(`Login attempt for deactivated user: ${username}`);
            throw new common_1.UnauthorizedException('Account is deactivated');
        }
        const isPasswordValid = await user.validatePassword(password);
        if (!isPasswordValid) {
            this.logger.warn(`Login attempt with invalid password for user: ${username}`);
            throw new common_1.UnauthorizedException('Invalid credentials');
        }
        await this.userRepository.update(user.id, {
            lastLoginAt: new Date(),
        });
        const payload = {
            sub: user.id,
            username: user.username,
            email: user.email,
            role: user.role,
        };
        const accessToken = this.jwtService.sign(payload);
        const expiresIn = this.getTokenExpirationTime();
        this.logger.log(`User ${username} logged in successfully`);
        return {
            accessToken,
            tokenType: 'Bearer',
            expiresIn,
            user: this.mapToUserInfo(user),
        };
    }
    async validateUserById(userId) {
        try {
            const user = await this.userRepository.findOne({
                where: { id: userId, isActive: true },
            });
            return user;
        }
        catch (error) {
            this.logger.error(`Error validating user by ID: ${userId}`, error.stack);
            return null;
        }
    }
    async findUserByUsername(username) {
        try {
            const user = await this.userRepository.findOne({
                where: [{ username }, { email: username }],
            });
            return user;
        }
        catch (error) {
            this.logger.error(`Error finding user by username: ${username}`, error.stack);
            return null;
        }
    }
    async getUserProfile(userId) {
        const user = await this.userRepository.findOne({
            where: { id: userId },
        });
        if (!user) {
            throw new common_1.NotFoundException('User not found');
        }
        return this.mapToUserInfo(user);
    }
    mapToUserInfo(user) {
        return {
            id: user.id,
            username: user.username,
            email: user.email,
            role: user.role,
            isActive: user.isActive,
            lastLoginAt: user.lastLoginAt,
        };
    }
    getTokenExpirationTime() {
        const expiresIn = this.configService.get('jwt.expiresIn', '24h');
        if (expiresIn.endsWith('h')) {
            return parseInt(expiresIn.slice(0, -1)) * 3600;
        }
        else if (expiresIn.endsWith('m')) {
            return parseInt(expiresIn.slice(0, -1)) * 60;
        }
        else if (expiresIn.endsWith('s')) {
            return parseInt(expiresIn.slice(0, -1));
        }
        else if (expiresIn.endsWith('d')) {
            return parseInt(expiresIn.slice(0, -1)) * 86400;
        }
        return 86400;
    }
};
exports.AuthService = AuthService;
exports.AuthService = AuthService = AuthService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(user_entity_1.User)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        jwt_1.JwtService,
        config_1.ConfigService])
], AuthService);
//# sourceMappingURL=auth.service.js.map