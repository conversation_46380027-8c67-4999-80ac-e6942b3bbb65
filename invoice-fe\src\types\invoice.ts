export interface Invoice {
  invoiceId: number;
  invoiceType: string;
  adjustmentType: string;
  templateCode: string;
  invoiceSeri: string;
  invoiceNumber: string;
  invoiceNo: string;
  currency: string;
  total: number;
  issueDate: number;
  issueDateStr: string | null;
  state: string | null;
  requestDate: string | null;
  description: string | null;
  buyerIdNo: string | null;
  stateCode: string | null;
  subscriberNumber: string | null;
  paymentStatus: number;
  viewStatus: number;
  downloadStatus: string | null;
  exchangeStatus: string | null;
  numOfExchange: string | null;
  createTime: number;
  contractId: string | null;
  contractNo: string | null;
  supplierTaxCode: string;
  buyerTaxCode: string;
  totalBeforeTax: number;
  taxAmount: number;
  taxRate: string | null;
  paymentMethod: string | null;
  paymentTime: string | null;
  customerId: string | null;
  buyerName: string;
  no: string | null;
  paymentStatusName: string | null;
  originalInvoiceId: string;
}

export interface GetInvoicesRequest {
  apiToken: string;
  startDate: string;
  endDate: string;
  rowPerPage: number;
  pageNum: number;
  invoiceNo?: string;
  invoiceType?: string;
  buyerTaxCode?: string;
  buyerIdNo?: string;
  templateCode?: string;
  invoiceSeri?: string;
  getAll?: boolean;
  issueStartDate?: string;
  issueEndDate?: string;
  adjustmentType?: string;
}

export interface GetInvoicesResponse {
  errorCode: string | null;
  description: string | null;
  totalRow: number;
  invoices: Invoice[];
}

export interface InvoiceFilters {
  startDate?: string;
  endDate?: string;
  invoiceNo?: string;
  invoiceType?: string;
  buyerTaxCode?: string;
  templateCode?: string;
  invoiceSeri?: string;
}

export interface InvoiceState {
  invoices: Invoice[];
  isLoading: boolean;
  error: string | null;
  filters: InvoiceFilters;
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface InvoiceStore extends InvoiceState {
  // Data fetching
  fetchInvoices: (params?: { page?: number; limit?: number }) => Promise<void>;

  // State management
  setFilters: (filters: Partial<InvoiceFilters>) => void;
  clearError: () => void;

  // Pagination
  setPage: (page: number) => void;
  setLimit: (limit: number) => void;
}
