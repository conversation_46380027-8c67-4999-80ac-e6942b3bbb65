"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var CustomerService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.CustomerService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const customer_entity_1 = require("../../../database/entities/customer.entity");
const user_entity_1 = require("../../../database/entities/user.entity");
const sinvoice_service_1 = require("./sinvoice.service");
let CustomerService = CustomerService_1 = class CustomerService {
    customerRepository;
    userRepository;
    dataSource;
    sinvoiceService;
    logger = new common_1.Logger(CustomerService_1.name);
    constructor(customerRepository, userRepository, dataSource, sinvoiceService) {
        this.customerRepository = customerRepository;
        this.userRepository = userRepository;
        this.dataSource = dataSource;
        this.sinvoiceService = sinvoiceService;
    }
    async createCustomer(createCustomerDto) {
        const { username, email, password, sinvoiceUsername, sinvoicePassword } = createCustomerDto;
        const existingUser = await this.userRepository.findOne({
            where: [{ username }, { email }],
        });
        if (existingUser) {
            if (existingUser.username === username) {
                throw new common_1.ConflictException('Username already exists');
            }
            if (existingUser.email === email) {
                throw new common_1.ConflictException('Email already exists');
            }
        }
        const queryRunner = this.dataSource.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();
        try {
            const user = this.userRepository.create({
                username,
                email,
                password,
                role: user_entity_1.UserRole.CUSTOMER,
                isActive: true,
            });
            const savedUser = await queryRunner.manager.save(user);
            const customer = this.customerRepository.create({
                userId: savedUser.id,
                sinvoiceUsername,
            });
            customer.setSinvoicePassword(sinvoicePassword);
            const savedCustomer = await queryRunner.manager.save(customer);
            await queryRunner.commitTransaction();
            this.logger.log(`Customer created successfully: ${username} (${savedUser.id})`);
            const customerWithUser = await this.customerRepository.findOne({
                where: { id: savedCustomer.id },
                relations: ['user'],
            });
            return this.mapToCustomerResponse(customerWithUser);
        }
        catch (error) {
            await queryRunner.rollbackTransaction();
            this.logger.error(`Failed to create customer: ${username}`, error.stack);
            throw error;
        }
        finally {
            await queryRunner.release();
        }
    }
    async getCustomerById(id) {
        const customer = await this.customerRepository.findOne({
            where: { id },
            relations: ['user'],
        });
        if (!customer) {
            throw new common_1.NotFoundException('Customer not found');
        }
        return this.mapToCustomerResponse(customer);
    }
    async getCustomerByUserId(userId) {
        const customer = await this.customerRepository.findOne({
            where: { userId },
            relations: ['user'],
        });
        if (!customer) {
            throw new common_1.NotFoundException('Customer not found');
        }
        return this.mapToCustomerResponse(customer);
    }
    async getCustomerByApiToken(apiToken) {
        return this.customerRepository.findOne({
            where: { apiToken },
            relations: ['user'],
        });
    }
    async updateCustomer(id, updateCustomerDto) {
        const customer = await this.customerRepository.findOne({
            where: { id },
            relations: ['user'],
        });
        if (!customer) {
            throw new common_1.NotFoundException('Customer not found');
        }
        const { username, email, password, isActive, sinvoiceUsername, sinvoicePassword, } = updateCustomerDto;
        const queryRunner = this.dataSource.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();
        try {
            if (username && username !== customer.user.username) {
                const existingUser = await this.userRepository.findOne({
                    where: { username },
                });
                if (existingUser && existingUser.id !== customer.userId) {
                    throw new common_1.ConflictException('Username already exists');
                }
                customer.user.username = username;
            }
            if (email && email !== customer.user.email) {
                const existingUser = await this.userRepository.findOne({
                    where: { email },
                });
                if (existingUser && existingUser.id !== customer.userId) {
                    throw new common_1.ConflictException('Email already exists');
                }
                customer.user.email = email;
            }
            if (password) {
                customer.user.password = password;
            }
            if (typeof isActive === 'boolean') {
                customer.user.isActive = isActive;
            }
            if (username || email || password || typeof isActive === 'boolean') {
                await queryRunner.manager.save(customer.user);
            }
            if (sinvoiceUsername) {
                customer.sinvoiceUsername = sinvoiceUsername;
            }
            if (sinvoicePassword) {
                customer.setSinvoicePassword(sinvoicePassword);
                customer.clearSinvoiceTokens();
            }
            const savedCustomer = await queryRunner.manager.save(customer);
            await queryRunner.commitTransaction();
            this.logger.log(`Customer updated successfully: ${customer.user.username} (${id})`);
            const updatedCustomer = await this.customerRepository.findOne({
                where: { id },
                relations: ['user'],
            });
            return this.mapToCustomerResponse(updatedCustomer);
        }
        catch (error) {
            await queryRunner.rollbackTransaction();
            this.logger.error(`Failed to update customer: ${id}`, error.stack);
            throw error;
        }
        finally {
            await queryRunner.release();
        }
    }
    async resetApiToken(id) {
        const customer = await this.customerRepository.findOne({
            where: { id },
            relations: ['user'],
        });
        if (!customer) {
            throw new common_1.NotFoundException('Customer not found');
        }
        customer.regenerateApiToken();
        await this.customerRepository.save(customer);
        this.logger.log(`API token reset for customer: ${customer.user.username} (${id})`);
        return {
            apiToken: customer.apiToken,
            message: 'API token reset successfully',
        };
    }
    async updateSinvoiceTokens(id, accessToken, refreshToken, expiresIn) {
        const customer = await this.customerRepository.findOne({
            where: { id },
        });
        if (!customer) {
            throw new common_1.NotFoundException('Customer not found');
        }
        customer.updateSinvoiceTokens(accessToken, refreshToken, expiresIn);
        await this.customerRepository.save(customer);
        this.logger.log(`Sinvoice tokens updated for customer: ${id}`);
    }
    async getAllCustomers() {
        const customers = await this.customerRepository.find({
            relations: ['user'],
            order: { createdAt: 'DESC' },
        });
        return customers.map((customer) => this.mapToCustomerResponse(customer));
    }
    async sinvoiceLogin(apiToken) {
        const customer = await this.getCustomerByApiToken(apiToken);
        if (!customer) {
            throw new common_1.UnauthorizedException('Invalid API token');
        }
        try {
            if (!customer.sinvoicePasswordAuthTag) {
                this.logger.log(`Migrating legacy password for customer: ${customer.user.username}`);
                customer.migrateLegacyPassword();
                await this.customerRepository.save(customer);
            }
            const sinvoiceUsername = customer.sinvoiceUsername;
            const sinvoicePassword = customer.getSinvoicePassword();
            const sinvoiceResponse = await this.sinvoiceService.login(sinvoiceUsername, sinvoicePassword);
            await this.updateSinvoiceTokens(customer.id, sinvoiceResponse.access_token, sinvoiceResponse.refresh_token, sinvoiceResponse.expires_in);
            this.logger.log(`Sinvoice login successful for customer: ${customer.user.username}`);
            return {
                accessToken: sinvoiceResponse.access_token,
                tokenType: sinvoiceResponse.token_type,
                refreshToken: sinvoiceResponse.refresh_token,
                expiresIn: sinvoiceResponse.expires_in,
                scope: sinvoiceResponse.scope,
                iat: sinvoiceResponse.iat,
                invoiceCluster: sinvoiceResponse.invoice_cluster,
                type: sinvoiceResponse.type,
                jti: sinvoiceResponse.jti,
                message: 'Sinvoice login successful',
            };
        }
        catch (error) {
            this.logger.error(`Sinvoice login failed for customer: ${customer.user.username}`, error.stack);
            throw error;
        }
    }
    async refreshSinvoiceToken(customerId) {
        const customer = await this.customerRepository.findOne({
            where: { id: customerId },
            relations: ['user'],
        });
        if (!customer) {
            throw new common_1.NotFoundException('Customer not found');
        }
        if (!customer.sinvoiceRefreshToken) {
            throw new common_1.BadRequestException('No refresh token available');
        }
        try {
            const sinvoiceResponse = await this.sinvoiceService.refreshToken(customer.sinvoiceRefreshToken);
            await this.updateSinvoiceTokens(customer.id, sinvoiceResponse.access_token, sinvoiceResponse.refresh_token, sinvoiceResponse.expires_in);
            this.logger.log(`Sinvoice token refreshed for customer: ${customer.user.username}`);
            return {
                accessToken: sinvoiceResponse.access_token,
                tokenType: sinvoiceResponse.token_type,
                refreshToken: sinvoiceResponse.refresh_token,
                expiresIn: sinvoiceResponse.expires_in,
                scope: sinvoiceResponse.scope,
                iat: sinvoiceResponse.iat,
                invoiceCluster: sinvoiceResponse.invoice_cluster,
                type: sinvoiceResponse.type,
                jti: sinvoiceResponse.jti,
                message: 'Sinvoice token refreshed successfully',
            };
        }
        catch (error) {
            this.logger.error(`Sinvoice token refresh failed for customer: ${customer.user.username}`, error.stack);
            customer.clearSinvoiceTokens();
            await this.customerRepository.save(customer);
            throw error;
        }
    }
    async deleteCustomer(id) {
        const customer = await this.customerRepository.findOne({
            where: { id },
            relations: ['user'],
        });
        if (!customer) {
            throw new common_1.NotFoundException('Customer not found');
        }
        const queryRunner = this.dataSource.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();
        try {
            await queryRunner.manager.remove(customer);
            await queryRunner.manager.remove(customer.user);
            await queryRunner.commitTransaction();
            this.logger.log(`Customer deleted successfully: ${customer.user.username} (${id})`);
        }
        catch (error) {
            await queryRunner.rollbackTransaction();
            this.logger.error(`Failed to delete customer: ${id}`, error.stack);
            throw error;
        }
        finally {
            await queryRunner.release();
        }
    }
    mapToCustomerResponse(customer) {
        return {
            id: customer.id,
            user: {
                id: customer.user.id,
                username: customer.user.username,
                email: customer.user.email,
                role: customer.user.role,
                isActive: customer.user.isActive,
                lastLoginAt: customer.user.lastLoginAt,
                createdAt: customer.user.createdAt,
                updatedAt: customer.user.updatedAt,
            },
            sinvoiceUsername: customer.sinvoiceUsername,
            apiToken: customer.apiToken,
            hasSinvoiceTokens: !customer.isSinvoiceTokenExpired() && !!customer.sinvoiceAccessToken,
            sinvoiceTokenExpiresAt: customer.sinvoiceTokenExpiresAt,
            createdAt: customer.createdAt,
            updatedAt: customer.updatedAt,
        };
    }
};
exports.CustomerService = CustomerService;
exports.CustomerService = CustomerService = CustomerService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(customer_entity_1.Customer)),
    __param(1, (0, typeorm_1.InjectRepository)(user_entity_1.User)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.DataSource,
        sinvoice_service_1.SinvoiceService])
], CustomerService);
//# sourceMappingURL=customer.service.js.map