{"version": 3, "file": "ssl.util.js", "sourceRoot": "", "sources": ["../../../../src/common/utils/ssl.util.ts"], "names": [], "mappings": ";;;AAAA,yBAAyB;AACzB,6BAA6B;AAC7B,2CAAwC;AAExC,MAAM,MAAM,GAAG,IAAI,eAAM,CAAC,SAAS,CAAC,CAAC;AAKrC,MAAa,OAAO;IAIlB,MAAM,CAAC,wBAAwB,CAAC,SAI/B;QACC,IAAI,CAAC;YACH,KAAK,MAAM,CAAC,IAAI,EAAE,QAAQ,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE,CAAC;gBACzD,IAAI,QAAQ,EAAE,CAAC;oBACb,MAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;oBAC5C,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,YAAY,CAAC,EAAE,CAAC;wBACjC,MAAM,CAAC,KAAK,CAAC,OAAO,IAAI,gCAAgC,YAAY,EAAE,CAAC,CAAC;wBACxE,OAAO,KAAK,CAAC;oBACf,CAAC;oBAGD,EAAE,CAAC,UAAU,CAAC,YAAY,EAAE,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;oBAC/C,MAAM,CAAC,GAAG,CAAC,OAAO,IAAI,2BAA2B,YAAY,EAAE,CAAC,CAAC;gBACnE,CAAC;YACH,CAAC;YACD,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;YAClE,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAKD,MAAM,CAAC,oBAAoB,CAAC,QAA4C;QACtE,MAAM,UAAU,GAAG;YACjB,kBAAkB,EAAE,KAAK;SAC1B,CAAC;QAEF,QAAQ,QAAQ,EAAE,CAAC;YACjB,KAAK,KAAK;gBACR,OAAO;oBACL,GAAG,UAAU;oBAEb,OAAO,EAAE,SAAS;iBACnB,CAAC;YAEJ,KAAK,KAAK;gBACR,OAAO;oBACL,GAAG,UAAU;oBAEb,OAAO,EAAE,SAAS;iBACnB,CAAC;YAEJ,KAAK,OAAO;gBACV,OAAO;oBACL,GAAG,UAAU;oBAEb,OAAO,EAAE,SAAS;iBACnB,CAAC;YAEJ,KAAK,QAAQ;gBACX,OAAO;oBACL,kBAAkB,EAAE,IAAI;oBACxB,OAAO,EAAE,aAAa;iBACvB,CAAC;YAEJ;gBACE,OAAO,UAAU,CAAC;QACtB,CAAC;IACH,CAAC;IAKD,MAAM,CAAC,UAAU,CAAC,SAAc;QAC9B,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,MAAM,CAAC,GAAG,CAAC,wBAAwB,CAAC,CAAC;YACrC,OAAO;QACT,CAAC;QAED,MAAM,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC;QACpC,MAAM,CAAC,GAAG,CAAC,4BAA4B,SAAS,CAAC,kBAAkB,EAAE,CAAC,CAAC;QAEvE,IAAI,SAAS,CAAC,OAAO,EAAE,CAAC;YACtB,MAAM,CAAC,GAAG,CAAC,aAAa,SAAS,CAAC,OAAO,EAAE,CAAC,CAAC;QAC/C,CAAC;QAED,IAAI,SAAS,CAAC,EAAE,EAAE,CAAC;YACjB,MAAM,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC;QAC3C,CAAC;QAED,IAAI,SAAS,CAAC,IAAI,EAAE,CAAC;YACnB,MAAM,CAAC,GAAG,CAAC,gCAAgC,CAAC,CAAC;QAC/C,CAAC;QAED,IAAI,SAAS,CAAC,GAAG,EAAE,CAAC;YAClB,MAAM,CAAC,GAAG,CAAC,wBAAwB,CAAC,CAAC;QACvC,CAAC;IACH,CAAC;IAKD,MAAM,CAAC,0BAA0B;QAC/B,MAAM,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC;QACrC,MAAM,UAAU,GAAG,OAAO,CAAC,GAAG,CAAC,cAAc,KAAK,MAAM,CAAC;QAEzD,IAAI,CAAC,UAAU,EAAE,CAAC;YAChB,OAAO,KAAK,CAAC;QACf,CAAC;QAGD,IAAI,OAAO,KAAK,YAAY,EAAE,CAAC;YAC7B,MAAM,aAAa,GAAG,OAAO,CAAC,GAAG,CAAC,iBAAuD,CAAC;YAE1F,IAAI,aAAa,EAAE,CAAC;gBAClB,OAAO,IAAI,CAAC,oBAAoB,CAAC,aAAa,CAAC,CAAC;YAClD,CAAC;YAGD,OAAO;gBACL,kBAAkB,EAAE,OAAO,CAAC,GAAG,CAAC,0BAA0B,KAAK,OAAO;gBACtE,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,WAAW,IAAI,SAAS;aAC9C,CAAC;QACJ,CAAC;QAGD,OAAO;YACL,kBAAkB,EAAE,OAAO,CAAC,GAAG,CAAC,0BAA0B,KAAK,MAAM;YACrE,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,WAAW,IAAI,QAAQ;SAC7C,CAAC;IACJ,CAAC;CACF;AApID,0BAoIC"}