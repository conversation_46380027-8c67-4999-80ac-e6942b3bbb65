"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ApiTokenResponseDto = exports.ResetApiTokenDto = exports.UpdateCustomerDto = void 0;
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
class UpdateCustomerDto {
    username;
    email;
    password;
    isActive;
    sinvoiceUsername;
    sinvoicePassword;
}
exports.UpdateCustomerDto = UpdateCustomerDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Customer username',
        example: 'newusername',
        minLength: 3,
        maxLength: 50,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MinLength)(3),
    (0, class_validator_1.MaxLength)(50),
    __metadata("design:type", String)
], UpdateCustomerDto.prototype, "username", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Customer email address',
        example: '<EMAIL>',
        maxLength: 255,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEmail)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], UpdateCustomerDto.prototype, "email", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Customer password',
        example: 'newpassword123',
        minLength: 6,
        maxLength: 255,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MinLength)(6),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], UpdateCustomerDto.prototype, "password", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Customer account status',
        example: true,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], UpdateCustomerDto.prototype, "isActive", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Sinvoice username for API integration',
        example: 'new_sinvoice_user',
        minLength: 3,
        maxLength: 255,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MinLength)(3),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], UpdateCustomerDto.prototype, "sinvoiceUsername", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Sinvoice password for API integration',
        example: 'new_sinvoice_password',
        minLength: 6,
        maxLength: 255,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MinLength)(6),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], UpdateCustomerDto.prototype, "sinvoicePassword", void 0);
class ResetApiTokenDto {
    confirmation;
}
exports.ResetApiTokenDto = ResetApiTokenDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Confirmation message',
        example: 'RESET_TOKEN',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], ResetApiTokenDto.prototype, "confirmation", void 0);
class ApiTokenResponseDto {
    apiToken;
    message;
}
exports.ApiTokenResponseDto = ApiTokenResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'New API token',
        example: 'abc123def456...',
    }),
    __metadata("design:type", String)
], ApiTokenResponseDto.prototype, "apiToken", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Success message',
        example: 'API token reset successfully',
    }),
    __metadata("design:type", String)
], ApiTokenResponseDto.prototype, "message", void 0);
//# sourceMappingURL=update-customer.dto.js.map