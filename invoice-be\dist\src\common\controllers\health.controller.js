"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HealthController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const config_1 = require("@nestjs/config");
const typeorm_1 = require("typeorm");
const typeorm_2 = require("@nestjs/typeorm");
const public_decorator_1 = require("../decorators/public.decorator");
let HealthController = class HealthController {
    dataSource;
    configService;
    constructor(dataSource, configService) {
        this.dataSource = dataSource;
        this.configService = configService;
    }
    async getHealth() {
        const health = {
            status: 'ok',
            timestamp: new Date().toISOString(),
            uptime: process.uptime(),
            environment: this.configService.get('app.nodeEnv'),
            version: process.env.npm_package_version || '1.0.0',
            authentication: {
                jwtEnabled: !!this.configService.get('jwt.secret'),
                jwtExpiresIn: this.configService.get('jwt.expiresIn'),
            },
            database: {
                status: 'unknown',
                ssl: {
                    enabled: this.configService.get('database.ssl') !== false,
                    mode: process.env.DB_SSL_MODE || 'disabled',
                },
            },
        };
        try {
            await this.dataSource.query('SELECT 1');
            health.database.status = 'connected';
        }
        catch (error) {
            health.database.status = 'disconnected';
            health.status = 'error';
        }
        return health;
    }
    async getSSLStatus() {
        const sslConfig = this.configService.get('database.ssl');
        return {
            ssl: {
                enabled: sslConfig !== false,
                rejectUnauthorized: sslConfig?.rejectUnauthorized ?? false,
                mode: process.env.DB_SSL_MODE || 'disabled',
                certificates: {
                    ca: !!process.env.DB_SSL_CA_CERT_PATH,
                    client: !!process.env.DB_SSL_CLIENT_CERT_PATH,
                    key: !!process.env.DB_SSL_CLIENT_KEY_PATH,
                },
                cloudProvider: process.env.DB_CLOUD_PROVIDER || 'none',
            },
            environment: this.configService.get('app.nodeEnv'),
            timestamp: new Date().toISOString(),
        };
    }
};
exports.HealthController = HealthController;
__decorate([
    (0, public_decorator_1.Public)(),
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: 'Health check endpoint' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Service is healthy' }),
    (0, swagger_1.ApiResponse)({ status: 503, description: 'Service is unhealthy' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], HealthController.prototype, "getHealth", null);
__decorate([
    (0, public_decorator_1.Public)(),
    (0, common_1.Get)('ssl'),
    (0, swagger_1.ApiOperation)({ summary: 'SSL configuration check' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'SSL configuration details' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], HealthController.prototype, "getSSLStatus", null);
exports.HealthController = HealthController = __decorate([
    (0, swagger_1.ApiTags)('Health'),
    (0, common_1.Controller)('health'),
    __param(0, (0, typeorm_2.InjectDataSource)()),
    __metadata("design:paramtypes", [typeorm_1.DataSource,
        config_1.ConfigService])
], HealthController);
//# sourceMappingURL=health.controller.js.map