import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { CreateInvoiceDto } from '../dto/create-invoice.dto';
import { GetInvoicesDto } from '../dto/get-invoices.dto';
import { CustomerService } from '../../customers/services/customer.service';
type GetInvoicesSearchParams = Omit<GetInvoicesDto, 'apiToken'>;
export declare class InvoiceService {
    private readonly httpService;
    private readonly configService;
    private readonly customerService;
    private readonly logger;
    private readonly sinvoiceApiUrl;
    private readonly errorMessages;
    constructor(httpService: HttpService, configService: ConfigService, customerService: CustomerService);
    private handleSinvoiceError;
    createInvoice(apiToken: string, createInvoiceDto: CreateInvoiceDto): Promise<any>;
    getInvoices(apiToken: string, searchParams: GetInvoicesSearchParams): Promise<any>;
}
export {};
