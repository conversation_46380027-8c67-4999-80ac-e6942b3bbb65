import { ConfigService } from '@nestjs/config';
import { DataSource } from 'typeorm';
export declare class HealthController {
    private dataSource;
    private configService;
    constructor(dataSource: DataSource, configService: ConfigService);
    getHealth(): Promise<{
        status: string;
        timestamp: string;
        uptime: number;
        environment: any;
        version: string;
        authentication: {
            jwtEnabled: boolean;
            jwtExpiresIn: any;
        };
        database: {
            status: string;
            ssl: {
                enabled: boolean;
                mode: string;
            };
        };
    }>;
    getSSLStatus(): Promise<{
        ssl: {
            enabled: boolean;
            rejectUnauthorized: any;
            mode: string;
            certificates: {
                ca: boolean;
                client: boolean;
                key: boolean;
            };
            cloudProvider: string;
        };
        environment: any;
        timestamp: string;
    }>;
}
