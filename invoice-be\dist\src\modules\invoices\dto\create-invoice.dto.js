"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateInvoiceDto = exports.SummarizeInfoDto = exports.ItemInfoDto = exports.TaxBreakdownDto = exports.PaymentDto = exports.BuyerInfoDto = exports.SellerInfoDto = exports.GeneralInvoiceInfoDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
class GeneralInvoiceInfoDto {
    invoiceType;
    templateCode;
    invoiceSeries;
    currencyCode;
    adjustmentType;
    paymentStatus;
    cusGetInvoiceRight;
}
exports.GeneralInvoiceInfoDto = GeneralInvoiceInfoDto;
__decorate([
    (0, swagger_1.ApiProperty)({ example: '01GTKT' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], GeneralInvoiceInfoDto.prototype, "invoiceType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: '01GTKT0/002' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], GeneralInvoiceInfoDto.prototype, "templateCode", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 'C25TVA' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], GeneralInvoiceInfoDto.prototype, "invoiceSeries", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 'VND' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], GeneralInvoiceInfoDto.prototype, "currencyCode", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: '1' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], GeneralInvoiceInfoDto.prototype, "adjustmentType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: true }),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], GeneralInvoiceInfoDto.prototype, "paymentStatus", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: true }),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], GeneralInvoiceInfoDto.prototype, "cusGetInvoiceRight", void 0);
class SellerInfoDto {
    sellerLegalName;
    sellerTaxCode;
    sellerAddressLine;
    sellerPhoneNumber;
    sellerFaxNumber;
    sellerEmail;
    sellerBankName;
    sellerBankAccount;
    sellerDistrictName;
    sellerCityName;
    sellerCountryCode;
    sellerWebsite;
}
exports.SellerInfoDto = SellerInfoDto;
__decorate([
    (0, swagger_1.ApiProperty)({ example: 'Meo Cung Shop' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], SellerInfoDto.prototype, "sellerLegalName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: '**********-507' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], SellerInfoDto.prototype, "sellerTaxCode", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: '123 Đường ABC, Quận XYZ, Thành Phố ABC, Việt Nam' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], SellerInfoDto.prototype, "sellerAddressLine", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: '**********' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], SellerInfoDto.prototype, "sellerPhoneNumber", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: '**********' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], SellerInfoDto.prototype, "sellerFaxNumber", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: '<EMAIL>' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], SellerInfoDto.prototype, "sellerEmail", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 'Ngân hàng Vietcombank' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], SellerInfoDto.prototype, "sellerBankName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: '**********01' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], SellerInfoDto.prototype, "sellerBankAccount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: '' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], SellerInfoDto.prototype, "sellerDistrictName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 'Thành Phố Hà Nội' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], SellerInfoDto.prototype, "sellerCityName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: '84' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], SellerInfoDto.prototype, "sellerCountryCode", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 'sinvoice.viettel.vn' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], SellerInfoDto.prototype, "sellerWebsite", void 0);
class BuyerInfoDto {
    buyerName;
    buyerLegalName;
    buyerTaxCode;
    buyerAddressLine;
    buyerPostalCode;
    buyerDistrictName;
    buyerCityName;
    buyerCountryCode;
    buyerPhoneNumber;
    buyerFaxNumber;
    buyerEmail;
    buyerBankName;
    buyerBankAccount;
    buyerIdType;
    buyerIdNo;
    buyerCode;
    buyerBirthDay;
}
exports.BuyerInfoDto = BuyerInfoDto;
__decorate([
    (0, swagger_1.ApiProperty)({ example: 'Tên khách hàng' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], BuyerInfoDto.prototype, "buyerName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 'Tên đơn vị' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], BuyerInfoDto.prototype, "buyerLegalName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: '**********' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], BuyerInfoDto.prototype, "buyerTaxCode", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 'An Khánh Hoài Đức Hà Nội' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], BuyerInfoDto.prototype, "buyerAddressLine", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: '2342324323' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], BuyerInfoDto.prototype, "buyerPostalCode", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: 'Số 9, đường 11, VSIP Bắc Ninh, Thị xã Từ Sơn, Tỉnh',
    }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], BuyerInfoDto.prototype, "buyerDistrictName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 'Thành Phố Hà Nội' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], BuyerInfoDto.prototype, "buyerCityName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: '84' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], BuyerInfoDto.prototype, "buyerCountryCode", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: '987999999' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], BuyerInfoDto.prototype, "buyerPhoneNumber", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: '0458954' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], BuyerInfoDto.prototype, "buyerFaxNumber", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: '<EMAIL>' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], BuyerInfoDto.prototype, "buyerEmail", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 'Ngân hàng Quân đội MB' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], BuyerInfoDto.prototype, "buyerBankName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: '*****************' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], BuyerInfoDto.prototype, "buyerBankAccount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: '3' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], BuyerInfoDto.prototype, "buyerIdType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: '**********' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], BuyerInfoDto.prototype, "buyerIdNo", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: '832472343b_b' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], BuyerInfoDto.prototype, "buyerCode", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: '' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], BuyerInfoDto.prototype, "buyerBirthDay", void 0);
class PaymentDto {
    paymentMethodName;
}
exports.PaymentDto = PaymentDto;
__decorate([
    (0, swagger_1.ApiProperty)({ example: 'Truyền trực tiếp giá trị mong muốn vào đây' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], PaymentDto.prototype, "paymentMethodName", void 0);
class TaxBreakdownDto {
    taxPercentage;
    taxableAmount;
    taxAmount;
}
exports.TaxBreakdownDto = TaxBreakdownDto;
__decorate([
    (0, swagger_1.ApiProperty)({ example: 10 }),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], TaxBreakdownDto.prototype, "taxPercentage", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 3952730 }),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], TaxBreakdownDto.prototype, "taxableAmount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 395273 }),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], TaxBreakdownDto.prototype, "taxAmount", void 0);
class ItemInfoDto {
    lineNumber;
    itemCode;
    itemName;
    unitName;
    unitPrice;
    quantity;
    selection;
    itemTotalAmountWithoutTax;
    taxPercentage;
    taxAmount;
    discount;
    discount2;
    itemDiscount;
    itemNote;
    batchNo;
    expDate;
}
exports.ItemInfoDto = ItemInfoDto;
__decorate([
    (0, swagger_1.ApiProperty)({ example: 1 }),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], ItemInfoDto.prototype, "lineNumber", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 'HH0001' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ItemInfoDto.prototype, "itemCode", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 'Hàng hóa 01' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ItemInfoDto.prototype, "itemName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 'Chiếc' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ItemInfoDto.prototype, "unitName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 150450 }),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], ItemInfoDto.prototype, "unitPrice", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 10 }),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], ItemInfoDto.prototype, "quantity", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 1 }),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], ItemInfoDto.prototype, "selection", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 1504500 }),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], ItemInfoDto.prototype, "itemTotalAmountWithoutTax", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 10 }),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], ItemInfoDto.prototype, "taxPercentage", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 150450 }),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], ItemInfoDto.prototype, "taxAmount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: null }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], ItemInfoDto.prototype, "discount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: null }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], ItemInfoDto.prototype, "discount2", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 0 }),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], ItemInfoDto.prototype, "itemDiscount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: null }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], ItemInfoDto.prototype, "itemNote", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: null }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], ItemInfoDto.prototype, "batchNo", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: null }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], ItemInfoDto.prototype, "expDate", void 0);
class SummarizeInfoDto {
    extraName;
    extraValue;
}
exports.SummarizeInfoDto = SummarizeInfoDto;
__decorate([
    (0, swagger_1.ApiProperty)({ example: '{ Tiền phí đặc biệt, Tiền phí, }' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], SummarizeInfoDto.prototype, "extraName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: '{ 00 ,00,}' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], SummarizeInfoDto.prototype, "extraValue", void 0);
class CreateInvoiceDto {
    generalInvoiceInfo;
    sellerInfo;
    buyerInfo;
    payments;
    taxBreakdowns;
    itemInfo;
    summarizeInfo;
}
exports.CreateInvoiceDto = CreateInvoiceDto;
__decorate([
    (0, swagger_1.ApiProperty)({ type: GeneralInvoiceInfoDto }),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => GeneralInvoiceInfoDto),
    __metadata("design:type", GeneralInvoiceInfoDto)
], CreateInvoiceDto.prototype, "generalInvoiceInfo", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: SellerInfoDto }),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => SellerInfoDto),
    __metadata("design:type", SellerInfoDto)
], CreateInvoiceDto.prototype, "sellerInfo", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: BuyerInfoDto }),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => BuyerInfoDto),
    __metadata("design:type", BuyerInfoDto)
], CreateInvoiceDto.prototype, "buyerInfo", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: [PaymentDto] }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.ValidateNested)({ each: true }),
    (0, class_transformer_1.Type)(() => PaymentDto),
    __metadata("design:type", Array)
], CreateInvoiceDto.prototype, "payments", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: [TaxBreakdownDto] }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.ValidateNested)({ each: true }),
    (0, class_transformer_1.Type)(() => TaxBreakdownDto),
    __metadata("design:type", Array)
], CreateInvoiceDto.prototype, "taxBreakdowns", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: [ItemInfoDto] }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.ValidateNested)({ each: true }),
    (0, class_transformer_1.Type)(() => ItemInfoDto),
    __metadata("design:type", Array)
], CreateInvoiceDto.prototype, "itemInfo", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ type: SummarizeInfoDto }),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => SummarizeInfoDto),
    __metadata("design:type", SummarizeInfoDto)
], CreateInvoiceDto.prototype, "summarizeInfo", void 0);
//# sourceMappingURL=create-invoice.dto.js.map